-- Create an enum for vehicle types
create type public.vehicle_type as enum ('bicycle', 'scooter', 'car');

-- Create the vehicles table
create table public.vehicles (
  id uuid default gen_random_uuid() primary key,
  owner_id uuid references public.profiles(id) on delete cascade not null,
  type public.vehicle_type not null,
  make text not null,
  model text not null,
  year integer not null,
  hourly_rate numeric(10, 2) not null,
  daily_rate numeric(10, 2) not null,
  location geography(point, 4326) not null,
  is_available boolean default true,
  description text,
  features text[],
  images text[],
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  constraint valid_year check (year >= 1900 and year <= extract(year from now()) + 1),
  constraint valid_rates check (hourly_rate >= 0 and daily_rate >= 0)
);

-- Enable RLS
alter table public.vehicles enable row level security;

-- Create indexes for better performance
create index idx_vehicles_owner_id on public.vehicles(owner_id);
create index idx_vehicles_location on public.vehicles using gist(location);
create index idx_vehicles_availability on public.vehicles(is_available) where is_available = true;

-- Policies for vehicles
-- Anyone can view available vehicles
create policy "Vehicles are viewable by everyone." 
on vehicles for select 
to authenticated, anon
using (true);

-- Only owners can modify their vehicles
create policy "Users can insert their own vehicles." 
on vehicles for insert 
to authenticated
with check (auth.uid() = owner_id);

create policy "Users can update their own vehicles." 
on vehicles for update 
to authenticated
using (auth.uid() = owner_id);

create policy "Users can delete their own vehicles." 
on vehicles for delete 
to authenticated
using (auth.uid() = owner_id);

-- Function to update updated_at timestamp
create or replace function public.update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Create trigger for updated_at
create trigger update_vehicles_updated_at
before update on public.vehicles
for each row
execute function public.update_updated_at_column();
