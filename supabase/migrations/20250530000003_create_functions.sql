-- Function to find available vehicles within a radius and time range
create or replace function public.find_available_vehicles(
  p_latitude double precision,
  p_longitude double precision,
  p_radius_km double precision,
  p_start_time timestamp with time zone,
  p_end_time timestamp with time zone,
  p_vehicle_type public.vehicle_type default null,
  p_max_daily_rate numeric default null,
  p_limit integer default 50,
  p_offset integer default 0
) returns table (
  id uuid,
  type public.vehicle_type,
  make text,
  model text,
  year integer,
  hourly_rate numeric,
  daily_rate numeric,
  distance_km double precision,
  owner_name text,
  owner_rating numeric,
  features text[],
  images text[],
  description text
) as $$
begin
  return query
  select 
    v.id,
    v.type,
    v.make,
    v.model,
    v.year,
    v.hourly_rate,
    v.daily_rate,
    st_distance(
      st_makepoint(p_longitude, p_latitude)::geography,
      v.location
    ) / 1000 as distance_km,
    p.full_name as owner_name,
    (
      select avg(rating) 
      from public.reviews 
      where reviewee_id = v.owner_id
    ) as owner_rating,
    v.features,
    v.images,
    v.description
  from 
    public.vehicles v
    join public.profiles p on v.owner_id = p.id
  where 
    v.is_available = true
    and (p_vehicle_type is null or v.type = p_vehicle_type)
    and (p_max_daily_rate is null or v.daily_rate <= p_max_daily_rate)
    and st_dwithin(
      st_makepoint(p_longitude, p_latitude)::geography,
      v.location,
      p_radius_km * 1000
    )
    and not exists (
      select 1
      from public.bookings b
      where 
        b.vehicle_id = v.id
        and b.status in ('confirmed', 'in_progress')
        and (
          (p_start_time >= b.start_time and p_start_time < b.end_time) or
          (p_end_time > b.start_time and p_end_time <= b.end_time) or
          (p_start_time <= b.start_time and p_end_time >= b.end_time)
        )
    )
  order by 
    st_distance(
      st_makepoint(p_longitude, p_latitude)::geography,
      v.location
    )
  limit p_limit
  offset p_offset;
end;
$$ language plpgsql security definer;

-- Function to create a new booking with validation
create or replace function public.create_booking(
  p_vehicle_id uuid,
  p_start_time timestamp with time zone,
  p_end_time timestamp with time zone,
  p_notes text default null
) returns public.bookings as $$
declare
  v_vehicle_owner_id uuid;
  v_booking_cost numeric;
  v_booking_id uuid;
  v_booking public.bookings;
begin
  -- Validate booking time (at least 1 hour)
  if p_end_time <= p_start_time or (p_end_time - p_start_time) < interval '1 hour' then
    raise exception 'Invalid booking time range. Minimum booking duration is 1 hour.';
  end if;
  
  -- Check if vehicle exists and get owner
  select owner_id into v_vehicle_owner_id
  from public.vehicles
  where id = p_vehicle_id
  and is_available = true;
  
  if v_vehicle_owner_id is null then
    raise exception 'Vehicle not found or not available for booking.';
  end if;
  
  -- Check if vehicle is already booked for the time period
  if exists (
    select 1
    from public.bookings b
    where 
      b.vehicle_id = p_vehicle_id
      and b.status in ('confirmed', 'in_progress')
      and (
        (p_start_time >= b.start_time and p_start_time < b.end_time) or
        (p_end_time > b.start_time and p_end_time <= b.end_time) or
        (p_start_time <= b.start_time and p_end_time >= b.end_time)
      )
  ) then
    raise exception 'Vehicle is not available for the selected time period.';
  end if;
  
  -- Calculate booking cost
  select public.calculate_booking_cost(p_vehicle_id, p_start_time, p_end_time)
  into v_booking_cost;
  
  -- Create the booking
  insert into public.bookings (
    vehicle_id,
    renter_id,
    start_time,
    end_time,
    total_cost,
    status,
    renter_notes
  ) values (
    p_vehicle_id,
    auth.uid(),
    p_start_time,
    p_end_time,
    v_booking_cost,
    'pending',
    p_notes
  )
  returning * into v_booking;
  
  -- TODO: Send notification to vehicle owner
  
  return v_booking;
end;
$$ language plpgsql security definer;
