#!/bin/bash

# GaadiSewa+ Deployment Script
# This script handles deployment to different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="staging"
PLATFORM="web"
SKIP_TESTS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV    Deployment environment (staging, production)"
            echo "  -p, --platform PLATFORM Target platform (web, android, ios)"
            echo "  --skip-tests            Skip running tests before deployment"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 -e production -p web"
            echo "  $0 -e staging -p android --skip-tests"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "🚀 GaadiSewa+ Deployment Script"
echo "==============================="
echo "Environment: $ENVIRONMENT"
echo "Platform: $PLATFORM"
echo ""

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
    exit 1
fi

# Validate platform
if [[ "$PLATFORM" != "web" && "$PLATFORM" != "android" && "$PLATFORM" != "ios" ]]; then
    print_error "Invalid platform: $PLATFORM. Must be 'web', 'android', or 'ios'"
    exit 1
fi

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

# Check environment file
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    print_error "Environment file $ENV_FILE not found"
    print_status "Creating template environment file..."
    cat > "$ENV_FILE" << EOF
# GaadiSewa+ Environment Configuration - $ENVIRONMENT
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
KHALTI_PUBLIC_KEY=your_khalti_public_key_here
APP_ENV=$ENVIRONMENT
DEBUG=false
EOF
    print_warning "Please update $ENV_FILE with your actual configuration"
    exit 1
fi

print_status "Using environment file: $ENV_FILE"

# Run tests unless skipped
if [ "$SKIP_TESTS" = false ]; then
    print_status "Running test suite..."
    if [ -f "scripts/run_tests.sh" ]; then
        ./scripts/run_tests.sh
    else
        print_warning "Test script not found, running basic tests..."
        flutter test
    fi
    print_success "Tests completed successfully"
else
    print_warning "Skipping tests as requested"
fi

# Clean and get dependencies
print_status "Preparing build environment..."
flutter clean
flutter pub get

# Generate code
print_status "Generating code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Build for the specified platform
print_status "Building for $PLATFORM..."

case $PLATFORM in
    web)
        print_status "Building web application..."
        flutter build web \
            --release \
            --dart-define-from-file="$ENV_FILE" \
            --web-renderer html \
            --base-href /
        
        BUILD_OUTPUT="build/web"
        print_success "Web build completed: $BUILD_OUTPUT"
        
        # Deploy to hosting service
        if [ "$ENVIRONMENT" = "production" ]; then
            print_status "Deploying to production web hosting..."
            
            # Firebase Hosting
            if command -v firebase &> /dev/null && [ -f "firebase.json" ]; then
                firebase deploy --only hosting:production
                print_success "Deployed to Firebase Hosting"
            
            # Vercel
            elif command -v vercel &> /dev/null; then
                vercel --prod --cwd build/web
                print_success "Deployed to Vercel"
            
            # Netlify
            elif command -v netlify &> /dev/null; then
                netlify deploy --prod --dir=build/web
                print_success "Deployed to Netlify"
            
            else
                print_warning "No deployment service configured"
                print_status "Build output available at: $BUILD_OUTPUT"
            fi
        else
            print_status "Staging build completed at: $BUILD_OUTPUT"
        fi
        ;;
        
    android)
        print_status "Building Android application..."
        
        if [ "$ENVIRONMENT" = "production" ]; then
            flutter build appbundle \
                --release \
                --dart-define-from-file="$ENV_FILE"
            BUILD_OUTPUT="build/app/outputs/bundle/release/app-release.aab"
            print_success "Android App Bundle built: $BUILD_OUTPUT"
            print_status "Upload to Google Play Console: https://play.google.com/console"
        else
            flutter build apk \
                --debug \
                --dart-define-from-file="$ENV_FILE"
            BUILD_OUTPUT="build/app/outputs/flutter-apk/app-debug.apk"
            print_success "Android APK built: $BUILD_OUTPUT"
        fi
        ;;
        
    ios)
        if [[ "$OSTYPE" != "darwin"* ]]; then
            print_error "iOS builds are only supported on macOS"
            exit 1
        fi
        
        print_status "Building iOS application..."
        
        if [ "$ENVIRONMENT" = "production" ]; then
            flutter build ipa \
                --release \
                --dart-define-from-file="$ENV_FILE"
            BUILD_OUTPUT="build/ios/ipa"
            print_success "iOS IPA built: $BUILD_OUTPUT"
            print_status "Upload to App Store Connect: https://appstoreconnect.apple.com"
        else
            flutter build ios \
                --debug \
                --dart-define-from-file="$ENV_FILE"
            BUILD_OUTPUT="build/ios"
            print_success "iOS build completed: $BUILD_OUTPUT"
        fi
        ;;
esac

# Post-deployment tasks
print_status "Running post-deployment tasks..."

# Create deployment summary
DEPLOYMENT_SUMMARY="deployment_summary_$(date +%Y%m%d_%H%M%S).txt"
cat > "$DEPLOYMENT_SUMMARY" << EOF
GaadiSewa+ Deployment Summary
============================
Date: $(date)
Environment: $ENVIRONMENT
Platform: $PLATFORM
Build Output: $BUILD_OUTPUT
Flutter Version: $(flutter --version | head -n 1)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "N/A")
Git Branch: $(git branch --show-current 2>/dev/null || echo "N/A")

Environment Configuration:
$(grep -v "KEY\|SECRET\|PASSWORD" "$ENV_FILE" || echo "Environment file not readable")

Build Status: SUCCESS
EOF

print_success "Deployment summary saved: $DEPLOYMENT_SUMMARY"

# Performance recommendations
echo ""
print_status "Performance Recommendations:"
echo "  📱 Test on real devices before releasing"
echo "  🔍 Monitor app performance and crash reports"
echo "  📊 Set up analytics and error tracking"
echo "  🔒 Verify security configurations"
echo "  🧪 Run user acceptance testing"

# Environment-specific recommendations
if [ "$ENVIRONMENT" = "production" ]; then
    echo ""
    print_warning "Production Deployment Checklist:"
    echo "  ☐ Verify all environment variables are correct"
    echo "  ☐ Test payment integration with real transactions"
    echo "  ☐ Verify database backups are configured"
    echo "  ☐ Set up monitoring and alerting"
    echo "  ☐ Prepare rollback plan"
    echo "  ☐ Update app store metadata and screenshots"
    echo "  ☐ Notify stakeholders of deployment"
fi

echo ""
print_success "Deployment completed successfully! 🎉"

if [ "$ENVIRONMENT" = "production" ]; then
    print_status "🌟 GaadiSewa+ is now live in production!"
else
    print_status "🧪 GaadiSewa+ staging deployment ready for testing"
fi

echo ""
print_status "Next steps:"
echo "  1. Test the deployed application thoroughly"
echo "  2. Monitor logs and performance metrics"
echo "  3. Gather user feedback"
echo "  4. Plan next iteration"
echo ""
