#!/bin/bash

# GaadiSewa+ Test Runner Script
# This script runs all tests for the application

set -e

echo "🚀 Starting GaadiSewa+ Test Suite"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Flutter version:"
flutter --version

# Clean and get dependencies
print_status "Cleaning project and getting dependencies..."
flutter clean
flutter pub get

# Generate code if needed
print_status "Generating code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run static analysis
print_status "Running static analysis..."
if flutter analyze; then
    print_success "Static analysis passed"
else
    print_warning "Static analysis found issues"
fi

# Run unit tests
print_status "Running unit tests..."
if flutter test --coverage; then
    print_success "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

# Generate coverage report
if command -v lcov &> /dev/null; then
    print_status "Generating coverage report..."
    genhtml coverage/lcov.info -o coverage/html
    print_success "Coverage report generated in coverage/html/"
else
    print_warning "lcov not installed, skipping coverage report generation"
fi

# Run widget tests specifically
print_status "Running widget tests..."
if flutter test test/features/vehicles/presentation/widgets/; then
    print_success "Widget tests passed"
else
    print_warning "Some widget tests failed"
fi

# Run integration tests (if devices are available)
print_status "Checking for available devices..."
DEVICES=$(flutter devices --machine | jq -r '.[].id' 2>/dev/null || echo "")

if [ -n "$DEVICES" ]; then
    print_status "Running integration tests..."
    if flutter test integration_test/; then
        print_success "Integration tests passed"
    else
        print_warning "Integration tests failed or no devices available"
    fi
else
    print_warning "No devices available for integration tests"
fi

# Check for TODO comments in critical files
print_status "Checking for TODO comments in critical paths..."
TODO_COUNT=$(grep -r "TODO" lib/features/*/data/repositories/ lib/features/*/domain/ --include="*.dart" | wc -l || echo "0")
if [ "$TODO_COUNT" -gt 0 ]; then
    print_warning "Found $TODO_COUNT TODO comments in critical paths"
    grep -r "TODO" lib/features/*/data/repositories/ lib/features/*/domain/ --include="*.dart" || true
else
    print_success "No TODO comments found in critical paths"
fi

# Check for print statements (should use logger instead)
print_status "Checking for print statements..."
PRINT_COUNT=$(grep -r "print(" lib/ --include="*.dart" | grep -v "// ignore:" | wc -l || echo "0")
if [ "$PRINT_COUNT" -gt 0 ]; then
    print_warning "Found $PRINT_COUNT print statements (consider using logger)"
else
    print_success "No print statements found"
fi

# Test build for different platforms
print_status "Testing build for web..."
if flutter build web --dart-define=FLUTTER_WEB_USE_SKIA=true; then
    print_success "Web build successful"
else
    print_error "Web build failed"
fi

# Test if the app can be built for Android
print_status "Testing Android build..."
if flutter build apk --debug; then
    print_success "Android debug build successful"
else
    print_warning "Android build failed"
fi

# Performance tests
print_status "Running performance checks..."
if flutter test test/ --reporter=json > test_results.json 2>/dev/null; then
    SLOW_TESTS=$(cat test_results.json | jq -r '.[] | select(.result == "success" and .time > 5000) | .description' 2>/dev/null || echo "")
    if [ -n "$SLOW_TESTS" ]; then
        print_warning "Found slow tests (>5s):"
        echo "$SLOW_TESTS"
    else
        print_success "No slow tests detected"
    fi
    rm -f test_results.json
fi

# Security checks
print_status "Running basic security checks..."

# Check for hardcoded secrets
SECRET_PATTERNS=("password" "secret" "key" "token" "api_key")
for pattern in "${SECRET_PATTERNS[@]}"; do
    MATCHES=$(grep -ri "$pattern.*=" lib/ --include="*.dart" | grep -v "// ignore:" | wc -l || echo "0")
    if [ "$MATCHES" -gt 0 ]; then
        print_warning "Found potential hardcoded $pattern"
    fi
done

# Check for HTTP URLs (should use HTTPS)
HTTP_COUNT=$(grep -r "http://" lib/ --include="*.dart" | grep -v "localhost" | wc -l || echo "0")
if [ "$HTTP_COUNT" -gt 0 ]; then
    print_warning "Found $HTTP_COUNT HTTP URLs (consider using HTTPS)"
else
    print_success "No insecure HTTP URLs found"
fi

# Final summary
echo ""
echo "=================================="
print_success "Test suite completed!"
echo ""
print_status "Summary:"
echo "  ✅ Static analysis"
echo "  ✅ Unit tests"
echo "  ✅ Widget tests"
echo "  ✅ Build tests"
echo "  ✅ Security checks"
echo ""

if [ -f "coverage/lcov.info" ]; then
    COVERAGE=$(lcov --summary coverage/lcov.info 2>/dev/null | grep "lines" | awk '{print $2}' || echo "N/A")
    print_status "Test coverage: $COVERAGE"
fi

print_success "All tests completed successfully! 🎉"
print_status "Ready for production deployment!"

echo ""
echo "Next steps:"
echo "  1. Review coverage report: open coverage/html/index.html"
echo "  2. Address any warnings mentioned above"
echo "  3. Run integration tests on real devices"
echo "  4. Deploy to staging environment"
echo ""
