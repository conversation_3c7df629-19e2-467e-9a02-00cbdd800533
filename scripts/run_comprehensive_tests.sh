#!/bin/bash

# GaadiSewa+ Comprehensive Test Runner
# This script runs all tests for the application

set -e

echo "🚀 Starting GaadiSewa+ Comprehensive Test Suite"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Flutter version:"
flutter --version

# Clean and get dependencies
print_status "Cleaning project and getting dependencies..."
flutter clean
flutter pub get

# Run code generation if needed
print_status "Running code generation..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run static analysis
print_status "Running static analysis..."
if flutter analyze; then
    print_success "Static analysis passed"
else
    print_warning "Static analysis found issues"
fi

# Run unit tests
print_status "Running unit tests..."
if flutter test test/unit/ --coverage; then
    print_success "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

# Run widget tests
print_status "Running widget tests..."
if flutter test test/widget/ --coverage; then
    print_success "Widget tests passed"
else
    print_error "Widget tests failed"
    exit 1
fi

# Run integration tests
print_status "Running integration tests..."
if flutter test test/integration/ --coverage; then
    print_success "Integration tests passed"
else
    print_error "Integration tests failed"
    exit 1
fi

# Run payment flow tests
print_status "Running payment flow tests..."
if flutter test test/features/payments/integration/ --coverage; then
    print_success "Payment flow tests passed"
else
    print_error "Payment flow tests failed"
    exit 1
fi

# Run booking flow tests
print_status "Running booking flow tests..."
if flutter test test/features/bookings/ --coverage; then
    print_success "Booking flow tests passed"
else
    print_warning "Some booking tests may have failed"
fi

# Generate coverage report
print_status "Generating coverage report..."
if command -v lcov &> /dev/null; then
    lcov --remove coverage/lcov.info \
        'lib/generated/*' \
        'lib/*.g.dart' \
        'lib/main.dart' \
        'test/*' \
        -o coverage/lcov_cleaned.info
    
    genhtml coverage/lcov_cleaned.info -o coverage/html
    print_success "Coverage report generated in coverage/html/"
else
    print_warning "lcov not installed, skipping HTML coverage report"
fi

# Test build for different platforms
print_status "Testing build for Android..."
if flutter build apk --debug; then
    print_success "Android debug build successful"
else
    print_warning "Android debug build failed"
fi

print_status "Testing build for iOS (if on macOS)..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    if flutter build ios --debug --no-codesign; then
        print_success "iOS debug build successful"
    else
        print_warning "iOS debug build failed"
    fi
else
    print_warning "Skipping iOS build (not on macOS)"
fi

print_status "Testing build for Web..."
if flutter build web; then
    print_success "Web build successful"
else
    print_warning "Web build failed"
fi

# Performance tests
print_status "Running performance tests..."
if flutter test test/performance/ --coverage; then
    print_success "Performance tests passed"
else
    print_warning "Performance tests may have failed"
fi

# Security tests
print_status "Running security tests..."
if flutter test test/security/ --coverage; then
    print_success "Security tests passed"
else
    print_warning "Security tests may have failed"
fi

# Final summary
echo ""
echo "================================================"
print_success "🎉 Comprehensive test suite completed!"
echo ""
print_status "Test Summary:"
echo "  ✅ Static Analysis"
echo "  ✅ Unit Tests"
echo "  ✅ Widget Tests"
echo "  ✅ Integration Tests"
echo "  ✅ Payment Flow Tests"
echo "  ✅ Build Tests"
echo ""
print_status "Next Steps:"
echo "  1. Review coverage report in coverage/html/"
echo "  2. Address any warnings or failed tests"
echo "  3. Run integration tests on real devices"
echo "  4. Deploy to staging environment"
echo ""
print_success "GaadiSewa+ is ready for production! 🚀"
