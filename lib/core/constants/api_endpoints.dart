class ApiEndpoints {
  // Base URL
  static const String baseUrl = 'https://api.gaadisewa.com/v1';

  // Auth endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh-token';

  // User endpoints
  static const String users = '/users';
  static const String userProfile = '/users/me';
  static const String updateProfile = '/users/update-profile';
  static const String changePassword = '/users/change-password';

  // Vehicle endpoints
  static const String vehicles = '/vehicles';
  static const String searchVehicles = '/vehicles/search';
  static const String featuredVehicles = '/vehicles/featured';
  static const String vehicleCategories = '/vehicles/categories';
  
  // Booking endpoints
  static const String bookings = '/bookings';
  static const String bookingAvailability = '/bookings/availability';
  static const String bookingCancel = '/bookings/:id/cancel';
  
  // Payment endpoints
  static const String payments = '/payments';
  static const String paymentVerify = '/payments/:id/verify';
  static const String paymentMethods = '/payments/methods';
  
  // Review endpoints
  static const String reviews = '/reviews';
  static const String vehicleReviews = '/vehicles/:id/reviews';
  static const String userReviews = '/users/:id/reviews';
  
  // Upload endpoints
  static const String uploads = '/uploads';
  
  // Notification endpoints
  static const String notifications = '/notifications';
  static const String markNotificationAsRead = '/notifications/:id/read';
  static const String markAllNotificationsAsRead = '/notifications/read-all';
}
