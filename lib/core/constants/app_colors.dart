import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF2E7D32); // Green 800
  static const Color primaryLight = Color(0xFF4CAF50); // Green 500
  static const Color primaryDark = Color(0xFF1B5E20); // Green 900
  static const Color accent = Color(0xFFFF9800); // Orange 500

  // Text colors
  static const Color textPrimary = Color(0xFF212121); // Grey 900
  static const Color textSecondary = Color(0xFF757575); // Grey 600
  static const Color textLight = Color(0xFFBDBDBD); // Grey 400

  // Background colors
  static const Color background = Color(0xFFF5F5F5); // Grey 100
  static const Color surface = Colors.white;
  static const Color cardBackground = Colors.white;

  // Status colors
  static const Color success = Color(0xFF4CAF50); // Green 500
  static const Color error = Color(0xFFE53935); // Red 600
  static const Color warning = Color(0xFFFFC107); // Amber 500
  static const Color info = Color(0xFF2196F3); // Blue 500

  // Booking status colors
  static const Color pending = Color(0xFFFFA726); // Orange 400
  static const Color confirmed = Color(0xFF66BB6A); // Green 400
  static const Color completed = Color(0xFF42A5F5); // Blue 400
  static const Color cancelled = Color(0xFFEF5350); // Red 400

  // Vehicle type colors
  static const Color bicycle = Color(0xFF8BC34A); // Light Green 500
  static const Color scooter = Color(0xFFFF9800); // Orange 500
  static const Color car = Color(0xFF3F51B5); // Indigo 500

  // Misc
  static const Color divider = Color(0xFFEEEEEE); // Grey 200
  static const Color disabled = Color(0xFFE0E0E0); // Grey 300
  static const Color shadow = Color(0x1A000000); // Black 10%
}
