import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AppConfig {
  static final AppConfig _instance = AppConfig._internal();

  // Supabase client
  late final SupabaseClient supabaseClient;

  // App settings
  final String appName;
  final String appVersion;
  final String environment;

  // API Keys
  final String googleMapsApiKey;

  factory AppConfig() {
    return _instance;
  }

  AppConfig._internal()
    : appName = dotenv.env['APP_NAME'] ?? 'GaadiSewa+',
      appVersion = dotenv.env['APP_VERSION'] ?? '1.0.0',
      environment = dotenv.env['ENVIRONMENT'] ?? 'development',
      googleMapsApiKey = dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';

  // Initialize app configuration
  static Future<void> initialize() async {
    // Load environment variables
    await dotenv.load(fileName: ".env");

    // Initialize Supabase
    await Supabase.initialize(
      url: dotenv.env['SUPABASE_URL'] ?? '',
      anonKey: dotenv.env['SUPABASE_ANON_KEY'] ?? '',
      debug: dotenv.env['ENVIRONMENT'] == 'development',
    );
    
    // Store the Supabase client instance
    _instance.supabaseClient = Supabase.instance.client;
  }

  // Helper methods
  bool get isProduction => environment == 'production';
  bool get isDevelopment => environment == 'development';

  // Singleton pattern
  static AppConfig get instance => _instance;
}
