import 'package:flutter/material.dart';
import 'package:gaadi_sewa/core/error/failures.dart';

class AppErrorWidget extends StatelessWidget {
  final Failure failure;
  final VoidCallback? onRetry;
  final String? retryText;

  const AppErrorWidget({
    Key? key,
    required this.failure,
    this.onRetry,
    this.retryText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red[400],
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              _getErrorMessage(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (failure is ServerFailure) ...[
              const SizedBox(height: 8),
              Text(
                'Status: ${(failure as ServerFailure).statusCode ?? 'Unknown'}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryText ?? 'Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getErrorMessage() {
    if (failure is NetworkFailure) {
      return 'No internet connection. Please check your network settings and try again.';
    } else if (failure is ServerFailure) {
      return (failure as ServerFailure).message;
    } else if (failure is NotFoundFailure) {
      return 'The requested resource was not found.';
    } else if (failure is AuthFailure) {
      return (failure as AuthFailure).message;
    } else if (failure is InvalidInputFailure) {
      return (failure as InvalidInputFailure).message;
    } else {
      return failure.toString();
    }
  }
}
