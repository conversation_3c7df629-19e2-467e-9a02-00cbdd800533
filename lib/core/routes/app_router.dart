import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/auth/presentation/screens/onboarding_screen.dart';
import 'package:gaadi_sewa/features/auth/presentation/screens/login_screen.dart';
import 'package:gaadi_sewa/features/auth/presentation/screens/signup_screen.dart';
import 'package:gaadi_sewa/features/auth/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/home/<USER>/screens/home_screen.dart';
import 'package:gaadi_sewa/features/messaging/presentation/screens/conversation_list_screen.dart';
import 'package:gaadi_sewa/features/messaging/presentation/screens/conversation_screen.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/screens/vehicle_detail_screen.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/screens/vehicle_list_screen.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/bookings/presentation/screens/booking_form_screen.dart';
import 'package:gaadi_sewa/features/bookings/presentation/screens/booking_list_screen.dart';
import 'package:gaadi_sewa/features/bookings/presentation/screens/booking_detail_screen.dart';
import 'package:gaadi_sewa/features/profile/presentation/screens/profile_screen.dart';
import 'package:gaadi_sewa/features/profile/presentation/screens/preferences_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);
  final isLoggedIn = authState != null;

  return GoRouter(
    initialLocation: isLoggedIn ? '/home' : '/',
    routes: [
      // Public routes
      GoRoute(
        path: '/',
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const SignupScreen(),
      ),

      // Protected routes (require authentication)
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/vehicles',
        builder: (context, state) {
          final type = state.extra as VehicleType?;
          return VehicleListScreen(initialVehicleType: type);
        },
      ),
      GoRoute(
        path: VehicleDetailScreen.routeName,
        builder: (context, state) {
          final vehicleId = state.pathParameters['id'] ?? '';
          return VehicleDetailScreen(vehicleId: vehicleId);
        },
      ),
      GoRoute(
        path: ConversationListScreen.routeName,
        builder: (context, state) => const ConversationListScreen(),
      ),
      GoRoute(
        path: ConversationScreen.routeName,
        builder: (context, state) {
          final conversationId = state.pathParameters['id'] ?? '';
          return ConversationScreen(conversationId: conversationId);
        },
      ),

      // Booking routes
      GoRoute(
        path: '/bookings',
        builder: (context, state) {
          final isOwnerView = state.extra as bool? ?? false;
          return BookingListScreen(isOwnerView: isOwnerView);
        },
      ),
      GoRoute(
        path: '/bookings/:id',
        builder: (context, state) {
          final bookingId = state.pathParameters['id']!;
          final isOwnerView = state.extra as bool? ?? false;
          return BookingDetailScreen(
            bookingId: bookingId,
            isOwnerView: isOwnerView,
          );
        },
      ),
      GoRoute(
        path: '/book/vehicle/:vehicleId',
        builder: (context, state) {
          final vehicle = state.extra as VehicleModel;
          return BookingFormScreen(vehicle: vehicle);
        },
      ),

      // Profile routes
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/preferences',
        builder: (context, state) => const PreferencesScreen(),
      ),
    ],

    // Handle redirections based on auth state
    redirect: (BuildContext context, GoRouterState state) {
      final isAuthPage = _isAuthPage(state.matchedLocation);

      // If user is not logged in and trying to access protected routes
      if (!isLoggedIn && !isAuthPage) {
        return '/login';
      }

      // If user is logged in and trying to access auth pages
      if (isLoggedIn && isAuthPage) {
        return '/home';
      }

      // No redirect needed
      return null;
    },

    // Handle errors
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('Page not found: ${state.uri.path}'),
      ),
    ),
  );
});

bool _isAuthPage(String path) {
  return path == '/' || path == '/login' || path == '/signup';
}

// Navigation helpers
void navigateToBookingList(BuildContext context, {bool isOwnerView = false}) {
  context.push('/bookings', extra: isOwnerView);
}

void navigateToBookingDetails(BuildContext context, String bookingId,
    {bool isOwnerView = false}) {
  context.push('/bookings/$bookingId', extra: isOwnerView);
}

void navigateToBookVehicle(BuildContext context, VehicleModel vehicle) {
  context.push('/book/vehicle/${vehicle.id}', extra: vehicle);
}

void navigateToProfile(BuildContext context) {
  context.push('/profile');
}

void navigateToPreferences(BuildContext context) {
  context.push('/preferences');
}
