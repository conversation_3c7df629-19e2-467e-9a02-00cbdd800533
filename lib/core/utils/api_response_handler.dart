import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/error/failures.dart';

/// A utility class for handling API responses and converting them to [Either] types.
class ApiResponseHandler {
  /// Handles an API call that returns a [Future] and converts any exceptions
  /// to appropriate [Failure] objects.
  ///
  /// Example:
  /// ```dart
  /// final result = await ApiResponseHandler.handle(
  ///   () => _apiClient.getData(),
  ///   errorMessage: 'Failed to load data',
  /// );
  /// ```
  static Future<Either<Failure, T>> handle<T>(
    Future<T> Function() apiCall, {
    String? errorMessage,
  }) async {
    try {
      final result = await apiCall();
      return Right(result);
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on NoInternetException catch (e) {
      return Left(NoInternetFailure(message: e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(message: e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        errors: e.errors,
      ));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
        stackTrace: e.stackTrace,
      ));
    } on CacheException catch (e) {
      return Left(CacheFailure(
        message: e.message,
        stackTrace: e.stackTrace,
      ));
    } on FormatException catch (e) {
      return Left(UnknownFailure(
        message: 'Data format error: ${e.message}',
        stackTrace: e.stackTrace,
      ));
    } catch (e, stackTrace) {
      return Left(UnknownFailure(
        message: errorMessage ?? 'An unexpected error occurred',
        stackTrace: stackTrace,
      ));
    }
  }

  /// Handles an API call that doesn't return a value (void).
  ///
  /// Example:
  /// ```dart
  /// final result = await ApiResponseHandler.handleVoid(
  ///   () => _apiClient.updateData(data),
  ///   errorMessage: 'Failed to update data',
  /// );
  /// ```
  static Future<Either<Failure, void>> handleVoid(
    Future<void> Function() apiCall, {
    String? errorMessage,
  }) async {
    try {
      await apiCall();
      return const Right(null);
    } catch (e, stackTrace) {
      return Left(_mapExceptionToFailure(
        e,
        stackTrace,
        errorMessage: errorMessage,
      ));
    }
  }

  /// Maps an exception to a [Failure] object.
  static Failure _mapExceptionToFailure(
    Object exception,
    StackTrace stackTrace, {
    String? errorMessage,
  }) {
    if (exception is UnauthorizedException) {
      return UnauthorizedFailure(
        message: exception.message,
        stackTrace: stackTrace,
      );
    } else if (exception is NoInternetException) {
      return NoInternetFailure(
        message: exception.message,
        stackTrace: stackTrace,
      );
    } else if (exception is TimeoutException) {
      return TimeoutFailure(
        message: exception.message,
        stackTrace: stackTrace,
      );
    } else if (exception is NotFoundException) {
      return NotFoundFailure(
        message: exception.message,
        stackTrace: stackTrace,
      );
    } else if (exception is ValidationException) {
      return ValidationFailure(
        message: exception.message,
        errors: exception.errors,
        stackTrace: stackTrace,
      );
    } else if (exception is ServerException) {
      return ServerFailure(
        message: exception.message,
        code: exception.code,
        stackTrace: stackTrace,
      );
    } else if (exception is CacheException) {
      return CacheFailure(
        message: exception.message,
        stackTrace: stackTrace,
      );
    } else {
      return UnknownFailure(
        message: errorMessage ?? 'An unexpected error occurred',
        stackTrace: stackTrace,
      );
    }
  }
}
