import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/core/services/local_storage_service.dart';
import 'package:gaadi_sewa/core/utils/logger.dart';

/// A utility class for handling API requests and responses
class ApiUtils {
  // Singleton pattern
  factory ApiUtils() => _instance;
  ApiUtils._internal();
  static final ApiUtils _instance = ApiUtils._internal();

  // Dio instance for making HTTP requests
  late final Dio _dio;

  // Network info for checking connectivity
  late final NetworkInfo _networkInfo;

  // Local storage for auth tokens
  late final LocalStorageService _localStorage;

  // Initialize the API client
  Future<void> initialize({
    required Connectivity connectivity,
    required String baseUrl,
    Duration connectTimeout = const Duration(seconds: 30),
    Duration receiveTimeout = const Duration(seconds: 30),
    List<Interceptor> interceptors = const [],
    bool enableLogging = kDebugMode,
  }) async {
    // Initialize dependencies
    _networkInfo = NetworkInfoImpl(connectivity: connectivity);
    _localStorage = await LocalStorageService.getInstance();
    final baseOptions = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      validateStatus: (status) => status != null && status < 500,
    );

    _dio = Dio(baseOptions);

    // Add default interceptors
    _dio.interceptors.addAll([
      // Add auth interceptor
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add auth token if available
          final token = _localStorage.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          return handler.next(options);
        },
        onError: (error, handler) async {
          // Handle 401 Unauthorized errors
          if (error.response?.statusCode == 401) {
            // Try to refresh token if possible
            try {
              final newToken = await _refreshToken();
              if (newToken != null) {
                // Update the request header with the new token
                error.requestOptions.headers['Authorization'] =
                    'Bearer $newToken';

                // Repeat the request with the new token
                final opts = Options(
                  method: error.requestOptions.method,
                  headers: error.requestOptions.headers,
                );

                final response = await _dio.request(
                  error.requestOptions.path,
                  options: opts,
                  data: error.requestOptions.data,
                  queryParameters: error.requestOptions.queryParameters,
                );

                return handler.resolve(response);
              }
            } catch (e) {
              // If token refresh fails, clear auth data and navigate to login
              await _localStorage.clearAuthData();
              // TODO: Navigate to login screen
              return handler.reject(error);
            }
          }
          return handler.next(error);
        },
      ),
      // Add logging interceptor in debug mode
      if (enableLogging)
        LogInterceptor(
          request: true,
          requestHeader: true,
          requestBody: true,
          responseHeader: true,
          responseBody: true,
          error: true,
        ),
      // Add any additional interceptors
      ...interceptors,
    ]);

    // Configure Dio to accept self-signed certificates in debug mode
    if (kDebugMode) {
      (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final client = HttpClient();
        client.badCertificateCallback = (cert, host, port) => true;
        return client;
      };
    }
  }

  /// Refreshes the authentication token
  Future<String?> _refreshToken() async {
    try {
      final refreshToken = _localStorage.getRefreshToken();
      if (refreshToken == null) return null;

      final response = await _dio.post(
        '/auth/refresh', // TODO: Add to ApiConstants
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        final newToken = response.data['access_token'];
        final newRefreshToken = response.data['refresh_token'];

        if (newToken != null) {
          await _localStorage.setAuthToken(newToken);
          if (newRefreshToken != null) {
            await _localStorage.setRefreshToken(newRefreshToken);
          }
          return newToken;
        }
      }
    } catch (e) {
      Logger.e('Failed to refresh token', error: e);
    }
    return null;
  }

  /// Makes a GET request to the specified endpoint
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _request(
      path,
      options: (options ?? Options()).copyWith(method: 'GET'),
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
    );
  }

  /// Makes a POST request to the specified endpoint
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _request(
      path,
      options: (options ?? Options()).copyWith(method: 'POST'),
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  /// Makes a PUT request to the specified endpoint
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _request(
      path,
      options: (options ?? Options()).copyWith(method: 'PUT'),
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  /// Makes a PATCH request to the specified endpoint
  Future<Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _request(
      path,
      options: (options ?? Options()).copyWith(method: 'PATCH'),
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  /// Makes a DELETE request to the specified endpoint
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _request(
      path,
      options: (options ?? Options()).copyWith(method: 'DELETE'),
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
    );
  }

  /// Handles file uploads
  Future<Response> upload(
    String path, {
    required String filePath,
    String? fileKey = 'file',
    Map<String, dynamic>? fields,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
  }) async {
    final formData = FormData.fromMap({
      if (fields != null) ...fields,
      fileKey!: await MultipartFile.fromFile(filePath),
    });

    return _request(
      path,
      options: (options ?? Options()).copyWith(
        method: 'POST',
        contentType: 'multipart/form-data',
      ),
      data: formData,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
    );
  }

  /// Handles file downloads
  Future<Response> download(
    String urlPath, {
    required String savePath,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _dio.download(
      urlPath,
      savePath,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
    );
  }

  /// Internal method to handle all requests
  Future<Response> _request(
    String path, {
    required Options options,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    // Check internet connection
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) {
      throw const NoInternetException();
    }

    try {
      final response = await _dio.request(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );

      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e, stackTrace) {
      Logger.e('Unexpected error', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Handles the API response
  Response _handleResponse(Response response) {
    final statusCode = response.statusCode ?? 500;

    if (statusCode >= 200 && statusCode < 300) {
      return response;
    } else {
      final errorData = response.data;
      final errorMessage = errorData is Map
          ? errorData['message'] ?? 'An error occurred'
          : 'An error occurred';

      switch (statusCode) {
        case 400:
          throw BadRequestException(
            errorMessage,
            errors: errorData is Map ? errorData['errors'] : null,
          );
        case 401:
          throw UnauthorizedException(message: errorMessage);
        case 403:
          throw ForbiddenException(errorMessage);
        case 404:
          throw NotFoundException(message: errorMessage);
        case 422:
          throw ValidationException(
            message: errorMessage,
            errors: errorData is Map ? errorData['errors'] : null,
          );
        case 429:
          throw RateLimitExceededException(errorMessage);
        case 500:
          throw ServerException(message: errorMessage);
        case 503:
          throw ServiceUnavailableException(errorMessage);
        default:
          throw ServerException(
            message: 'An error occurred with status code: $statusCode',
          );
      }
    }
  }

  /// Handles Dio errors
  Exception _handleDioError(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout) {
      return const TimeoutException();
    } else if (error.type == DioExceptionType.connectionError) {
      return const NoInternetException();
    } else if (error.response != null) {
      return _handleResponse(error.response!) as Exception;
    } else {
      return ServerException(
          message: error.message ?? 'An unknown error occurred');
    }
  }

  /// Cancels all pending requests
  void cancelRequests({CancelToken? cancelToken}) {
    if (cancelToken != null) {
      cancelToken.cancel('Request cancelled');
    } else {
      _dio.close(force: true);
    }
  }
}

/// Global instance of the API utils
final apiUtils = ApiUtils();
