import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';

/// Extension methods for the [Either] type to make it easier to work with
/// [Failure] types.
extension EitherX<L, R> on Either<L, R> {
  /// Returns the right value if this is a right, otherwise throws an exception.
  ///
  /// This is useful for cases where you are certain that the [Either] is a [Right].
  R get asRight => (this as Right).value;

  /// Returns the left value if this is a left, otherwise throws an exception.
  ///
  /// This is useful for cases where you are certain that the [Either] is a [Left].
  L get asLeft => (this as Left).value;

  /// Returns `true` if this is a [Right].
  bool get isRight => this is Right<L, R>;

  /// Returns `true` if this is a [Left].
  bool get isLeft => this is Left<L, R>;

  /// Maps the right side of the [Either] if it is a [Right].
  Either<L, R2> mapRight<R2>(R2 Function(R r) f) {
    return fold(
      (l) => Left(l),
      (r) => Right(f(r)),
    );
  }

  /// Maps the left side of the [Either] if it is a [Left].
  Either<L2, R> mapLeft<L2>(L2 Function(L l) f) {
    return fold(
      (l) => Left(f(l)),
      (r) => Right(r),
    );
  }

  /// Executes [onRight] if this is a [Right], otherwise does nothing.
  void onRight(void Function(R r) onRight) {
    if (isRight) {
      onRight(asRight);
    }
  }

  /// Executes [onLeft] if this is a [Left], otherwise does nothing.
  void onLeft(void Function(L l) onLeft) {
    if (isLeft) {
      onLeft(asLeft);
    }
  }

  /// Returns the value from this [Right] or the given [defaultValue] if this is a [Left].
  R getOrElse(R defaultValue) => fold((_) => defaultValue, (r) => r);

  /// Returns the value from this [Right] or computes it from the given function if this is a [Left].
  R getOrElseFn(R Function() orElse) => fold((_) => orElse(), (r) => r);

  /// Returns this [Right] or the given [defaultRight] if this is a [Left].
  Either<L, R> orElse(Either<L, R> defaultRight) => fold((_) => defaultRight, (_) => this);

  /// Returns this [Right] or computes it from the given function if this is a [Left].
  Either<L, R> orElseFn(Either<L, R> Function() orElse) => fold((_) => orElse(), (_) => this);
}

/// Extension methods specifically for [Either] with [Failure] as the left type.
extension EitherFailureX<F extends Failure, R> on Either<F, R> {
  /// Throws the failure if this is a [Left], otherwise returns the right value.
  ///
  /// This is useful for cases where you want to throw an exception if the operation failed.
  R getOrThrow() => fold(
        (failure) => throw failure,
        (r) => r,
      );
}

/// Extension methods specifically for [Future] of [Either].
extension FutureEitherX<L, R> on Future<Either<L, R>> {
  /// Maps the right side of the [Either] if the future completes with a [Right].
  Future<Either<L, R2>> mapRight<R2>(R2 Function(R r) f) async {
    return (await this).mapRight(f);
  }

  /// Maps the left side of the [Either] if the future completes with a [Left].
  Future<Either<L2, R>> mapLeft<L2>(L2 Function(L l) f) async {
    return (await this).mapLeft(f);
  }

  /// Executes [onRight] if the future completes with a [Right].
  Future<void> onRight(void Function(R r) onRight) async {
    (await this).onRight(onRight);
  }

  /// Executes [onLeft] if the future completes with a [Left].
  Future<void> onLeft(void Function(L l) onLeft) async {
    (await this).onLeft(onLeft);
  }
}
