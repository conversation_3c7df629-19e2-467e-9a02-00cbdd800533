import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/core/utils/logger.dart';

/// A utility class for common UI-related tasks
class UiUtils {
  // Private constructor to prevent instantiation
  UiUtils._();

  /// Hides the keyboard if it's visible
  static void hideKeyboard(BuildContext context) {
    try {
      final currentFocus = FocusScope.of(context);
      if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
        FocusManager.instance.primaryFocus?.unfocus();
      }
    } catch (e) {
      Logger.e('Failed to hide keyboard', error: e);
    }
  }

  /// Shows a snackbar with the given message
  static void showSnackBar({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
    bool showCloseIcon = true,
  }) {
    try {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.hideCurrentSnackBar();

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: backgroundColor ?? AppColors.primary,
          duration: duration,
          action: action,
          showCloseIcon: showCloseIcon,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          margin: const EdgeInsets.all(16.0),
        ),
      );
    } catch (e) {
      Logger.e('Failed to show snackbar', error: e);
    }
  }

  /// Shows an error snackbar
  static void showErrorSnackBar({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    showSnackBar(
      context: context,
      message: message,
      backgroundColor: AppColors.error,
      duration: duration,
      action: action,
    );
  }

  /// Shows a success snackbar
  static void showSuccessSnackBar({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    showSnackBar(
      context: context,
      message: message,
      backgroundColor: AppColors.success,
      duration: duration,
      action: action,
    );
  }

  /// Shows a loading dialog
  static void showLoadingDialog({
    required BuildContext context,
    String? message,
    bool barrierDismissible = false,
  }) {
    showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => PopScope(
        canPop: barrierDismissible,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              if (message != null) ...[
                const SizedBox(height: 16.0),
                Text(
                  message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Hides the current dialog if one is showing
  static void hideDialog(BuildContext context) {
    try {
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
    } catch (e) {
      Logger.e('Failed to hide dialog', error: e);
    }
  }

  /// Shows a confirmation dialog
  static Future<bool?> showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool barrierDismissible = true,
    bool isDestructiveAction = false,
  }) async {
    try {
      return await showDialog<bool>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: isDestructiveAction ? AppColors.error : null,
              ),
              child: Text(confirmText),
            ),
          ],
        ),
      );
    } catch (e) {
      Logger.e('Failed to show confirmation dialog', error: e);
      return null;
    }
  }

  /// Shows a bottom sheet with the given child widget
  static Future<T?> showBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = false,
    bool useSafeArea = true,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
  }) async {
    try {
      return await showModalBottomSheet<T>(
        context: context,
        isScrollControlled: isScrollControlled,
        useSafeArea: useSafeArea,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        backgroundColor:
            backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
        elevation: elevation,
        shape: shape ??
            const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
            ),
        clipBehavior: clipBehavior,
        builder: (context) => child,
      );
    } catch (e) {
      Logger.e('Failed to show bottom sheet', error: e);
      return null;
    }
  }

  /// Copies text to clipboard and shows a snackbar
  static void copyToClipboard({
    required BuildContext context,
    required String text,
    String? successMessage = 'Copied to clipboard',
  }) {
    try {
      Clipboard.setData(ClipboardData(text: text));
      if (successMessage != null) {
        showSnackBar(context: context, message: successMessage);
      }
    } catch (e) {
      Logger.e('Failed to copy to clipboard', error: e);
      showErrorSnackBar(
        context: context,
        message: 'Failed to copy to clipboard',
      );
    }
  }

  /// Gets the screen size
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// Gets the screen width
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Gets the screen height
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Gets the safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Gets the keyboard height
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// Checks if the keyboard is visible
  static bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// Gets the text scale factor
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaleFactor;
  }

  /// Gets the device pixel ratio
  static double getDevicePixelRatio(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  /// Gets the orientation of the device
  static Orientation getOrientation(BuildContext context) {
    return MediaQuery.of(context).orientation;
  }

  /// Checks if the device is in portrait mode
  static bool isPortrait(BuildContext context) {
    return getOrientation(context) == Orientation.portrait;
  }

  /// Checks if the device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return getOrientation(context) == Orientation.landscape;
  }

  /// Gets the status bar height
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// Gets the bottom safe area height
  static double getBottomSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// Gets the app bar height
  static double getAppBarHeight(BuildContext context) {
    return kToolbarHeight;
  }

  /// Gets the height of the system UI (status bar + app bar)
  static double getSystemUiHeight(BuildContext context) {
    return getStatusBarHeight(context) + getAppBarHeight(context);
  }

  /// Gets the height of the bottom navigation bar
  static double getBottomNavigationBarHeight() {
    return kBottomNavigationBarHeight;
  }

  /// Gets the height of the system UI including the bottom navigation bar
  static double getTotalSystemUiHeight(BuildContext context) {
    return getSystemUiHeight(context) + getBottomNavigationBarHeight();
  }

  /// Gets the height of the keyboard
  static double getKeyboardHeightFromContext(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// Gets the height of the keyboard with safe area
  static double getKeyboardHeightWithSafeArea(BuildContext context) {
    final viewInsets = MediaQuery.of(context).viewInsets;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return viewInsets.bottom - bottomPadding;
  }

  /// Shows a date picker dialog
  static Future<DateTime?> showDatePickerDialog({
    required BuildContext context,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    DatePickerMode initialDatePickerMode = DatePickerMode.day,
    Locale? locale,
    bool useRootNavigator = true,
    RouteSettings? routeSettings,
    TextDirection? textDirection,
    TransitionBuilder? builder,
    DatePickerEntryMode initialEntryMode = DatePickerEntryMode.calendar,
    String? helpText,
    String? cancelText,
    String? confirmText,
    String? saveText,
    String? errorFormatText,
    String? errorInvalidText,
    String? fieldHintText,
    String? fieldLabelText,
    String? restorationId,
    void Function(DatePickerEntryMode)? onDatePickerModeChange,
  }) async {
    try {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate ?? DateTime.now(),
        firstDate: firstDate ?? DateTime(1900),
        lastDate: lastDate ?? DateTime(2100),
        initialDatePickerMode: initialDatePickerMode,
        locale: locale,
        useRootNavigator: useRootNavigator,
        routeSettings: routeSettings,
        textDirection: textDirection,
        builder: builder,
        initialEntryMode: initialEntryMode,
        helpText: helpText,
        cancelText: cancelText,
        confirmText: confirmText,
        errorFormatText: errorFormatText,
        errorInvalidText: errorInvalidText,
        fieldHintText: fieldHintText,
        fieldLabelText: fieldLabelText,
        // restorationId parameter is not supported in this Flutter version
        onDatePickerModeChange: onDatePickerModeChange,
      );
      return picked;
    } catch (e) {
      Logger.e('Failed to show date picker', error: e);
      return null;
    }
  }

  /// Shows a time picker dialog
  static Future<TimeOfDay?> showTimePickerDialog({
    required BuildContext context,
    required TimeOfDay initialTime,
    bool useRootNavigator = true,
    TimePickerEntryMode initialEntryMode = TimePickerEntryMode.dial,
    String? cancelText,
    String? confirmText,
    String? helpText,
    String? errorInvalidText,
    String? hourLabelText,
    String? minuteLabelText,
    RouteSettings? routeSettings,
    void Function(TimePickerEntryMode)? onEntryModeChanged,
    Offset? anchorPoint,
  }) async {
    try {
      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: initialTime,
        useRootNavigator: useRootNavigator,
        initialEntryMode: initialEntryMode,
        cancelText: cancelText,
        confirmText: confirmText,
        helpText: helpText,
        errorInvalidText: errorInvalidText,
        hourLabelText: hourLabelText,
        minuteLabelText: minuteLabelText,
        routeSettings: routeSettings,
        onEntryModeChanged: onEntryModeChanged,
        anchorPoint: anchorPoint,
      );
      return picked;
    } catch (e) {
      Logger.e('Failed to show time picker', error: e);
      return null;
    }
  }
}
