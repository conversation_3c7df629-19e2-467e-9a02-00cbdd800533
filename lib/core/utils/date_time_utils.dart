import 'package:intl/intl.dart';

/// A utility class for handling date and time operations.
class DateTimeUtils {
  // Private constructors to prevent instantiation
  DateTimeUtils._();

  static final DateFormat _dateFormat = DateFormat('MMM d, yyyy');
  static final DateFormat _timeFormat = DateFormat('h:mm a');
  static final DateFormat _dateTimeFormat = DateFormat('MMM d, yyyy h:mm a');
  static final DateFormat _apiDateFormat = DateFormat('yyyy-MM-dd');
  static final DateFormat _apiDateTimeFormat = DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

  /// Gets today's date with time set to 00:00:00
  static DateTime get today => DateTime.now().toDateOnly();

  /// Gets the current date and time
  static DateTime get now => DateTime.now();

  /// Gets the current date and time in UTC
  static DateTime get nowUtc => DateTime.now().toUtc();

  /// Formats a [DateTime] to a date string (e.g., "Jan 1, 2023")
  static String formatDate(DateTime? date) {
    if (date == null) return '';
    return _dateFormat.format(date);
  }

  /// Formats a [DateTime] to a time string (e.g., "2:30 PM")
  static String formatTime(DateTime? time) {
    if (time == null) return '';
    return _timeFormat.format(time);
  }

  /// Formats a [DateTime] to a date and time string (e.g., "Jan 1, 2023 2:30 PM")
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return _dateTimeFormat.format(dateTime);
  }

  /// Formats a [DateTime] to an API date string (e.g., "2023-01-01")
  static String formatDateForApi(DateTime date) {
    return _apiDateFormat.format(date);
  }

  /// Formats a [DateTime] to an API date-time string (e.g., "2023-01-01T14:30:00Z")
  static String formatDateTimeForApi(DateTime dateTime) {
    return _apiDateTimeFormat.format(dateTime.toUtc());
  }

  /// Parses a date string from the API (e.g., "2023-01-01")
  static DateTime? parseDateFromApi(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return _apiDateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parses a date-time string from the API (e.g., "2023-01-01T14:30:00Z")
  static DateTime? parseDateTimeFromApi(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) return null;
    try {
      return _apiDateTimeFormat.parse(dateTimeString, true).toLocal();
    } catch (e) {
      return null;
    }
  }

  /// Gets the difference in days between two dates
  static int differenceInDays(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }

  /// Gets the difference in hours between two dates
  static int differenceInHours(DateTime start, DateTime end) {
    return end.difference(start).inHours;
  }

  
  /// Gets the difference in minutes between two dates
  static int differenceInMinutes(DateTime start, DateTime end) {
    return end.difference(start).inMinutes;
  }

  /// Checks if two dates are on the same day
  static bool isSameDay(DateTime? a, DateTime? b) {
    if (a == null || b == null) return false;
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  /// Gets the start of the day for the given date (00:00:00)
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Gets the end of the day for the given date (23:59:59.999)
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Adds days to a date and returns a new DateTime
  static DateTime addDays(DateTime date, int days) {
    return date.add(Duration(days: days));
  }

  /// Subtracts days from a date and returns a new DateTime
  static DateTime subtractDays(DateTime date, int days) {
    return date.subtract(Duration(days: days));
  }

  /// Checks if a date is today
  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }

  /// Checks if a date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Checks if a date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Gets a list of dates between two dates (inclusive)
  static List<DateTime> getDaysInBetween(DateTime startDate, DateTime endDate) {
    final days = <DateTime>[];
    var currentDate = startDate.toDateOnly();
    final end = endDate.toDateOnly();
    
    while (!currentDate.isAfter(end)) {
      days.add(currentDate);
      currentDate = currentDate.add(const Duration(days: 1));
    }
    
    return days;
  }

  /// Gets the age from a birth date
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    // Adjust age if birthday hasn't occurred yet this year
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  /// Gets a human-readable duration string (e.g., "2 days", "1 week", "3 months")
  static String getDurationString(Duration duration) {
    if (duration.inDays >= 30) {
      final months = (duration.inDays / 30).round();
      return months == 1 ? '1 month' : '$months months';
    } else if (duration.inDays >= 7) {
      final weeks = (duration.inDays / 7).round();
      return weeks == 1 ? '1 week' : '$weeks weeks';
    } else if (duration.inDays > 0) {
      return duration.inDays == 1 ? '1 day' : '${duration.inDays} days';
    } else if (duration.inHours > 0) {
      return duration.inHours == 1 ? '1 hour' : '${duration.inHours} hours';
    } else if (duration.inMinutes > 0) {
      return duration.inMinutes == 1 
          ? '1 minute' 
          : '${duration.inMinutes} minutes';
    } else {
      return 'Less than a minute';
    }
  }
}

/// Extension methods for [DateTime]
extension DateTimeExtension on DateTime {
  /// Converts a DateTime to a date-only DateTime (time set to 00:00:00)
  DateTime toDateOnly() {
    return DateTime(year, month, day);
  }

  /// Checks if this date is between two other dates (inclusive)
  bool isBetween(DateTime start, DateTime end) {
    return (isAfter(start) || isAtSameMomentAs(start)) && 
           (isBefore(end) || isAtSameMomentAs(end));
  }

  /// Gets the start of the week (Monday)
  DateTime startOfWeek() {
    // Monday is the first day of the week (index 1 in DateTime.weekday)
    return subtract(Duration(days: weekday - 1));
  }

  /// Gets the end of the week (Sunday)
  DateTime endOfWeek() {
    // Sunday is the last day of the week (index 7 in DateTime.weekday)
    return add(Duration(days: DateTime.daysPerWeek - weekday));
  }

  /// Gets the start of the month
  DateTime startOfMonth() {
    return DateTime(year, month);
  }

  /// Gets the end of the month
  DateTime endOfMonth() {
    return DateTime(year, month + 1, 0, 23, 59, 59, 999);
  }

  /// Gets the start of the year
  DateTime startOfYear() {
    return DateTime(year);
  }

  /// Gets the end of the year
  DateTime endOfYear() {
    return DateTime(year, 12, 31, 23, 59, 59, 999);
  }

  /// Adds months to the current date
  DateTime addMonths(int months) {
    return DateTime(year, month + months, day, hour, minute, second, millisecond, microsecond);
  }

  /// Subtracts months from the current date
  DateTime subtractMonths(int months) {
    return addMonths(-months);
  }

  /// Gets the number of days in the month
  int get daysInMonth {
    return DateTime(year, month + 1, 0).day;
  }

  /// Checks if the year is a leap year
  bool get isLeapYear {
    return (year % 4 == 0) && ((year % 100 != 0) || (year % 400 == 0));
  }

  /// Gets the quarter (1-4) of the year
  int get quarter {
    return ((month - 1) ~/ 3) + 1;
  }
}
