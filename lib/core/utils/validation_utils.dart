import 'package:intl/intl.dart';

/// A utility class for form validation.
class ValidationUtils {
  // Private constructor to prevent instantiation
  ValidationUtils._();

  /// Validates if the input is not empty
  static String? validateRequired(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validates if the input is a valid email address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(
      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}\$',
      caseSensitive: false,
      multiLine: false,
    );

    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validates if the input is a valid phone number
  static String? validatePhoneNumber(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }

    // Basic phone number validation (10 digits, optional + at start)
    final phoneRegex = RegExp(r'^(\+?\d{1,3}?[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\$');
    
    if (!phoneRegex.hasMatch(value)) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validates if the input is a valid password
  static String? validatePassword(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }

    if (!value.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }

    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }

    if (!value.contains(RegExp(r'[!@#\$%^&*(),.?\":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  /// Validates if the input is a valid date of birth (must be at least 18 years old)
  static String? validateDateOfBirth(DateTime? date, {bool isRequired = true}) {
    if (!isRequired && date == null) {
      return null;
    }

    if (date == null) {
      return 'Date of birth is required';
    }

    final now = DateTime.now();
    final minDate = DateTime(now.year - 120, now.month, now.day);
    final maxDate = DateTime(now.year - 18, now.month, now.day);

    if (date.isBefore(minDate)) {
      return 'Please enter a valid date of birth';
    }

    if (date.isAfter(maxDate)) {
      return 'You must be at least 18 years old';
    }

    return null;
  }

  /// Validates if the input is a valid date range
  static String? validateDateRange({
    required DateTime? startDate,
    required DateTime? endDate,
    String? startDateLabel,
    String? endDateLabel,
    bool isRequired = true,
  }) {
    if (!isRequired && (startDate == null || endDate == null)) {
      return null;
    }

    if (startDate == null) {
      return '${startDateLabel ?? 'Start date'} is required';
    }

    if (endDate == null) {
      return '${endDateLabel ?? 'End date'} is required';
    }

    if (endDate.isBefore(startDate)) {
      return '${endDateLabel ?? 'End date'} must be after ${startDateLabel ?? 'start date'}';
    }

    return null;
  }

  /// Validates if the input is a valid credit card number
  static String? validateCreditCardNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Card number is required';
    }

    // Remove any non-digit characters
    final cleaned = value.replaceAll(RegExp(r'[^0-9]'), '');

    // Check if the card number is valid using Luhn algorithm
    if (cleaned.length < 13 || cleaned.length > 19) {
      return 'Please enter a valid card number';
    }

    // Luhn algorithm implementation
    int sum = 0;
    bool isSecond = false;

    for (int i = cleaned.length - 1; i >= 0; i--) {
      int digit = int.parse(cleaned[i]);

      if (isSecond) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit ~/ 10) + (digit % 10);
        }
      }

      sum += digit;
      isSecond = !isSecond;
    }


    if (sum % 10 != 0) {
      return 'Please enter a valid card number';
    }

    return null;
  }

  /// Validates if the input is a valid CVV
  static String? validateCVV(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return 'CVV is required';
    }

    final cvvRegex = RegExp(r'^[0-9]{3,4}\$');
    
    if (!cvvRegex.hasMatch(value)) {
      return 'Please enter a valid CVV (3 or 4 digits)';
    }

    return null;
  }

  /// Validates if the input is a valid expiry date (MM/YY format)
  static String? validateExpiryDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Expiry date is required';
    }

    final expiryRegex = RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})\$');
    
    if (!expiryRegex.hasMatch(value)) {
      return 'Please enter a valid expiry date (MM/YY)';
    }

    final parts = value.split('/');
    final month = int.parse(parts[0]);
    final year = int.parse('20${parts[1]}');
    
    final now = DateTime.now();
    final expiryDate = DateTime(year, month + 1, 0); // Last day of the month
    
    if (expiryDate.isBefore(now)) {
      return 'This card has expired';
    }

    return null;
  }

  /// Validates if the input is a valid name (allows letters, spaces, hyphens, and apostrophes)
  static String? validateName(String? value, {String? fieldName, bool isRequired = true}) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'Name'} is required';
    }

    final nameRegex = RegExp(r"^[a-zA-Z\s'-]+\$");
    
    if (!nameRegex.hasMatch(value)) {
      return 'Please enter a valid name';
    }

    return null;
  }

  /// Validates if the input is a valid numeric value
  static String? validateNumeric(String? value, {String? fieldName, bool isRequired = true}) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }

    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }

    return null;
  }

  /// Validates if the input is a valid positive number
  static String? validatePositiveNumber(
    String? value, {
    String? fieldName,
    bool isRequired = true,
    double? minValue,
    double? maxValue,
  }) {
    final numericError = validateNumeric(value, fieldName: fieldName, isRequired: isRequired);
    if (numericError != null) return numericError;

    if (value == null || value.isEmpty) return null;

    final number = double.parse(value);
    
    if (minValue != null && number < minValue) {
      return 'Value must be at least $minValue';
    }
    
    if (maxValue != null && number > maxValue) {
      return 'Value must be at most $maxValue';
    }

    if (number <= 0) {
      return 'Value must be greater than zero';
    }


    return null;
  }

  /// Validates if the input is a valid URL
  static String? validateUrl(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return 'URL is required';
    }

    final urlRegex = RegExp(
      r'^(https?:\/\/)?' // protocol
      r'(([a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]*\.)+[a-zA-Z]{2,}|' // domain name
      r'((\d{1,3}\.){3}\d{1,3}))' // OR ip (v4) address
      r'(\:\d+)?(\/[-a-zA-Z0-9%_.~+]*)*' // port and path
      r'(\?[;&a-zA-Z0-9%_.~+=-]*)?' // query string
      r'(\#[-a-zA-Z0-9_]*)?\$', // fragment locator
      caseSensitive: false,
    );

    if (!urlRegex.hasMatch(value)) {
      return 'Please enter a valid URL';
    }

    return null;
  }

  /// Validates if the input matches a regular expression pattern
  static String? validatePattern(
    String? value, 
    Pattern pattern, 
    String errorMessage, {
    bool isRequired = true,
  }) {
    if (!isRequired && (value == null || value.isEmpty)) {
      return null;
    }

    if (value == null || value.isEmpty) {
      return 'This field is required';
    }

    if (!RegExp(pattern.toString()).hasMatch(value)) {
      return errorMessage;
    }

    return null;
  }

  /// Validates if the input matches another field (for password confirmation)
  static String? validateMatch(
    String? value, 
    String? matchValue, 
    String fieldName,
  ) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your $fieldName';
    }

    if (value != matchValue) {
      return '$fieldName does not match';
    }

    return null;
  }
}
