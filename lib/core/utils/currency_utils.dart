import 'package:intl/intl.dart';

/// A utility class for handling currency formatting and calculations.
class CurrencyUtils {
  // Private constructor to prevent instantiation
  CurrencyUtils._();

  /// The default currency symbol
  static const String defaultCurrency = 'NPR';
  static const String defaultCurrencySymbol = 'Rs.';

  /// The number of decimal places to show for currency values
  static const int defaultDecimalDigits = 2;

  /// The default locale to use for formatting
  static const String defaultLocale = 'en_NP';

  /// Formats a number as a currency string with the specified currency code
  ///
  /// Example:
  /// ```dart
  /// CurrencyUtils.format(1000); // 'Rs. 1,000.00'
  /// CurrencyUtils.format(1000, symbol: '\$'); // '\$1,000.00'
  /// CurrencyUtils.format(1000, decimalDigits: 0); // 'Rs. 1,000'
  /// ```
  static String format(
    num amount, {
    String? symbol,
    int? decimalDigits,
    String? locale,
  }) {
    final currencySymbol = symbol ?? defaultCurrencySymbol;
    final digits = decimalDigits ?? defaultDecimalDigits;
    final formatLocale = locale ?? defaultLocale;

    final formatString = '$currencySymbol #,##0';
    final decimalFormat = digits > 0 ? '.' + '0' * digits : '';
    
    return NumberFormat('$formatString$decimalFormat', formatLocale).format(amount);
  }

  /// Parses a currency string to a double value
  ///
  /// Example:
  /// ```dart
  /// CurrencyUtils.parse('Rs. 1,000.50'); // 1000.5
  /// CurrencyUtils.parse('\$1,000.50'); // 1000.5
  /// ```
  static double? parse(
    String? amountString, {
    String? symbol,
    String? locale,
  }) {
    if (amountString == null || amountString.isEmpty) return null;

    final currencySymbol = symbol ?? defaultCurrencySymbol;
    final formatLocale = locale ?? defaultLocale;
    
    // Remove all non-numeric characters except decimal point and minus sign
    final cleanString = amountString
        .replaceAll(RegExp(r'[^\d.-]'), '');
    
    if (cleanString.isEmpty) return null;

    try {
      return NumberFormat.currency(
        locale: formatLocale,
        symbol: currencySymbol,
      ).parse(cleanString) as double;
    } catch (e) {
      // Fallback to simple parsing if formatting fails
      return double.tryParse(cleanString);
    }
  }

  /// Calculates the total amount with tax and discount
  ///
  /// [amount] - The base amount
  /// [taxRate] - The tax rate as a percentage (e.g., 13 for 13%)
  /// [discountAmount] - The discount amount to subtract before tax
  /// [discountPercent] - The discount percentage to apply before tax
  /// [round] - Whether to round the result to 2 decimal places
  ///
  /// Returns a map with the calculated values:
  /// - 'subtotal': The amount after discount, before tax
  /// - 'tax': The calculated tax amount
  /// - 'discount': The total discount applied
  /// - 'total': The final total amount
  static Map<String, double> calculateTotal({
    required double amount,
    double taxRate = 0.0,
    double discountAmount = 0.0,
    double discountPercent = 0.0,
    bool round = true,
  }) {
    // Calculate discount amounts
    final percentDiscount = amount * (discountPercent / 100);
    final totalDiscount = discountAmount + percentDiscount;
    
    // Apply discount
    var subtotal = amount - totalDiscount;
    if (subtotal < 0) subtotal = 0;
    
    // Calculate tax
    final tax = subtotal * (taxRate / 100);
    
    // Calculate total
    var total = subtotal + tax;
    
    // Round values if needed
    if (round) {
      subtotal = double.parse(subtotal.toStringAsFixed(2));
      total = double.parse(total.toStringAsFixed(2));
    }
    
    return {
      'subtotal': subtotal,
      'tax': tax,
      'discount': totalDiscount,
      'total': total,
    };
  }

  /// Formats a price range string (e.g., "Rs. 1,000 - Rs. 2,000")
  static String formatPriceRange({
    required double minPrice,
    required double maxPrice,
    String? symbol,
    int? decimalDigits,
  }) {
    if (minPrice == maxPrice) {
      return format(minPrice, symbol: symbol, decimalDigits: decimalDigits);
    }
    
    return '${format(minPrice, symbol: symbol, decimalDigits: decimalDigits)} - ${format(maxPrice, symbol: symbol, decimalDigits: decimalDigits)}';
  }

  /// Converts an amount from one currency to another using a conversion rate
  static double convertCurrency({
    required double amount,
    required double conversionRate,
    int decimalPlaces = 2,
  }) {
    final result = amount * conversionRate;
    return double.parse(result.toStringAsFixed(decimalPlaces));
  }

  /// Formats a large number with K, M, B suffixes
  ///
  /// Example:
  /// ```dart
  /// CurrencyUtils.compact(1500); // '1.5K'
  /// CurrencyUtils.compact(2500000); // '2.5M'
  /// ```
  static String compact(num number, {int decimalPlaces = 1}) {
    if (number < 1000) return number.toString();
    
    final double numValue = number.toDouble();
    final exp = (number.toString().length - 1) ~/ 3 * 3;
    final scaled = numValue / (10.0 * exp);
    
    if (scaled >= 1000) {
      return '${(scaled / 1000).toStringAsFixed(decimalPlaces)}B';
    } else if (scaled >= 1) {
      return '${scaled.toStringAsFixed(decimalPlaces)}M';
    } else {
      return '${(scaled * 1000).toStringAsFixed(decimalPlaces)}K';
    }
  }

  /// Returns the currency symbol for a given currency code
  static String getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'INR':
        return '₹';
      case 'NPR':
        return 'Rs.';
      default:
        return currencyCode;
    }
  }
}
