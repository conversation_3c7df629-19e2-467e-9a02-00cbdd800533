import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gaadi_sewa/core/constants/api_constants.dart';
import 'package:gaadi_sewa/core/utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A utility class for handling app localization and language settings
class LocalizationUtils {
  // Singleton pattern
  factory LocalizationUtils() => _instance;
  LocalizationUtils._internal();
  static final LocalizationUtils _instance = LocalizationUtils._internal();

  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English (United States)
    Locale('ne', 'NP'), // Nepali (Nepal)
  ];

  // Default locale
  static const Locale defaultLocale = Locale('en', 'US');

  // Locale change stream controller
  final _localeController = StreamController<Locale>.broadcast();
  
  // Current locale
  Locale? _currentLocale;
  
  // SharedPreferences key for locale
  static const String _localeKey = 'app_locale';

  // Get current locale
  Locale? get currentLocale => _currentLocale;

  // Stream of locale changes
  Stream<Locale> get onLocaleChanged => _localeController.stream;

  // Initialize the localization utils
  Future<void> initialize() async {
    try {
      // Load saved locale from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final localeString = prefs.getString(_localeKey);
      
      if (localeString != null && localeString.isNotEmpty) {
        final parts = localeString.split('_');
        if (parts.length == 2) {
          _currentLocale = Locale(parts[0], parts[1]);
        } else {
          _currentLocale = Locale(parts[0]);
        }
      } else {
        // Use device locale if available, otherwise fallback to default
        final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;
        _currentLocale = isLocaleSupported(deviceLocale) 
            ? deviceLocale 
            : defaultLocale;
        
        // Save the determined locale
        await prefs.setString(
          _localeKey, 
          '${_currentLocale!.languageCode}_${_currentLocale!.countryCode ?? ''}',
        );
      }
      
      // Set the initial locale
      _localeController.add(_currentLocale!);
    } catch (e) {
      Logger.e('Failed to initialize localization', error: e);
      _currentLocale = defaultLocale;
    }
  }

  /// Check if a locale is supported by the app
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.any((l) => 
      l.languageCode == locale.languageCode && 
      (l.countryCode == null || l.countryCode == locale.countryCode)
    );
  }

  /// Change the current locale
  Future<void> setLocale(Locale newLocale) async {
    if (!isLocaleSupported(newLocale)) {
      Logger.w('Locale $newLocale is not supported');
      return;
    }

    try {
      // Save the new locale to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _localeKey, 
        '${newLocale.languageCode}_${newLocale.countryCode ?? ''}',
      );
      
      // Update current locale and notify listeners
      _currentLocale = newLocale;
      _localeController.add(newLocale);
      
      // Update app's locale resolution
      await _updateAppLocale(newLocale);
    } catch (e) {
      Logger.e('Failed to set locale', error: e);
      rethrow;
    }
  }

  /// Update the app's locale resolution
  Future<void> _updateAppLocale(Locale locale) async {
    try {
      // Set the app's locale resolution
      // Cannot directly set locale on platformDispatcher
      // Instead, we'll rely on the app to rebuild with the new locale
      
      // Force rebuild of the app
      final binding = WidgetsFlutterBinding.ensureInitialized();
      binding.performReassemble();
      
      // Update system overlays if needed
      if (locale.languageCode == 'ar' || 
          locale.languageCode == 'he' ||
          locale.languageCode == 'fa') {
        // Right-to-left languages
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
      } else {
        // Left-to-right languages
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      }
    } catch (e) {
      Logger.e('Failed to update app locale', error: e);
      rethrow;
    }
  }

  /// Get the display name of a locale
  String getLocaleName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'ne':
        return 'नेपाली';
      default:
        return locale.languageCode.toUpperCase();
    }
  }

  /// Get the flag emoji for a locale
  String getLocaleFlag(Locale locale) {
    switch (locale.countryCode?.toUpperCase()) {
      case 'US':
        return '🇺🇸';
      case 'NP':
        return '🇳🇵';
      default:
        return '🌐';
    }
  }

  /// Get the locale from language code
  Locale getLocaleFromLanguageCode(String languageCode) {
    return supportedLocales.firstWhere(
      (locale) => locale.languageCode == languageCode,
      orElse: () => defaultLocale,
    );
  }

  /// Get the current language code
  String get currentLanguageCode => _currentLocale?.languageCode ?? 'en';

  /// Check if the current locale is English
  bool get isEnglish => currentLanguageCode == 'en';

  /// Check if the current locale is Nepali
  bool get isNepali => currentLanguageCode == 'ne';

  /// Get the current locale as a string (e.g., 'en_US')
  String get currentLocaleString {
    if (_currentLocale == null) return 'en_US';
    return '${_currentLocale!.languageCode}_${_currentLocale!.countryCode ?? 'US}'}';
  }

  /// Get the current locale for API requests (e.g., 'en-US')
  String get currentApiLocale {
    if (_currentLocale == null) return 'en-US';
    return '${_currentLocale!.languageCode}-${_currentLocale!.countryCode ?? 'US'}';
  }

  /// Get the current locale for intl package
  String get currentIntlLocale {
    if (_currentLocale == null) return 'en_US';
    return '${_currentLocale!.languageCode}_${_currentLocale!.countryCode ?? 'US'}';
  }

  /// Get the current text direction
  TextDirection get currentTextDirection {
    final languageCode = _currentLocale?.languageCode ?? 'en';
    switch (languageCode) {
      case 'ar':
      case 'he':
      case 'fa':
        return TextDirection.rtl;
      default:
        return TextDirection.ltr;
    }
  }

  /// Get the current text alignment
  TextAlign get currentTextAlign {
    return currentTextDirection == TextDirection.rtl 
        ? TextAlign.right 
        : TextAlign.left;
  }

  /// Get the current edge insets for RTL support
  EdgeInsetsDirectional getEdgeInsets({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return currentTextDirection == TextDirection.rtl
        ? EdgeInsetsDirectional.only(
            start: end,
            top: top,
            end: start,
            bottom: bottom,
          )
        : EdgeInsetsDirectional.only(
            start: start,
            top: top,
            end: end,
            bottom: bottom,
          );
  }

  /// Get the current border radius for RTL support
  BorderRadius getBorderRadius({
    double topLeft = 0.0,
    double topRight = 0.0,
    double bottomLeft = 0.0,
    double bottomRight = 0.0,
  }) {
    return currentTextDirection == TextDirection.rtl
        ? BorderRadius.only(
            topLeft: Radius.circular(topRight),
            topRight: Radius.circular(topLeft),
            bottomLeft: Radius.circular(bottomRight),
            bottomRight: Radius.circular(bottomLeft),
          )
        : BorderRadius.only(
            topLeft: Radius.circular(topLeft),
            topRight: Radius.circular(topRight),
            bottomLeft: Radius.circular(bottomLeft),
            bottomRight: Radius.circular(bottomRight),
          );
  }

  /// Get the current alignment for RTL support
  Alignment getAlignment({
    double x = 0.0,
    double y = 0.0,
  }) {
    return currentTextDirection == TextDirection.rtl
        ? Alignment(-x, y)
        : Alignment(x, y);
  }

  /// Get the current text direction widget
  TextDirection getTextDirectionWidget(TextDirection? textDirection) {
    return textDirection ?? currentTextDirection;
  }

  /// Get the current text alignment widget
  TextAlign getTextAlignWidget(TextAlign? textAlign) {
    return textAlign ?? currentTextAlign;
  }

  /// Get the current edge insets for RTL support
  EdgeInsetsDirectional getEdgeInsetsWidget(EdgeInsetsGeometry? padding) {
    if (padding == null) return EdgeInsets.zero as EdgeInsetsDirectional;
    
    if (padding is EdgeInsets) {
      return currentTextDirection == TextDirection.rtl
          ? EdgeInsetsDirectional.only(
              start: padding.right,
              top: padding.top,
              end: padding.left,
              bottom: padding.bottom,
            )
          : padding as EdgeInsetsDirectional;
    } else {
      return padding as EdgeInsetsDirectional;
    }
  }

  /// Clean up resources
  void dispose() {
    _localeController.close();
  }
}

// Global instance of the localization utils
final localizationUtils = LocalizationUtils();
