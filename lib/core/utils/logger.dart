import 'dart:convert';
import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart' as logger_lib;

/// A utility class for logging messages with different severity levels.
///
/// This class provides a simple interface for logging messages with different
/// severity levels (verbose, debug, info, warning, error, wtf). It uses the
/// `logger` package under the hood for pretty-printed logs in the console.
///
/// In debug mode, logs are printed to the console with colors and emojis.
/// In release mode, only errors and warnings are logged.
class Logger {
  // Singleton pattern
  factory Logger() => _instance;
  Logger._internal();
  static final Logger _instance = Logger._internal();

  // Logger instance from the logger package
  late final logger_lib.Logger _logger;
  
  // Whether logging is enabled
  bool _enabled = true;
  
  // Log level for the application
  logger_lib.Level _logLevel = kDebugMode 
      ? logger_lib.Level.verbose 
      : logger_lib.Level.warning;

  // Initialize the logger
  void initialize({
    bool enabled = true,
    logger_lib.Level? logLevel,
    logger_lib.LogPrinter? printer,
  }) {
    _enabled = enabled;
    _logLevel = logLevel ?? _logLevel;
    
    _logger = logger_lib.Logger(
      level: _logLevel,
      printer: printer ?? logger_lib.PrettyPrinter(
        methodCount: 2, // Number of method calls to be shown
        errorMethodCount: 8, // Number of method calls if stacktrace is provided
        lineLength: 120, // Width of the output
        colors: true, // Colorful log messages
        printEmojis: true, // Print an emoji for each log message
        printTime: false, // Should each log print contain a timestamp
      ),
    );
  }

  // Log a verbose message
  static void v(
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _instance._log(
      logger_lib.Level.verbose,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  // Log a debug message
  static void d(
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _instance._log(
      logger_lib.Level.debug,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  // Log an info message
  static void i(
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _instance._log(
      logger_lib.Level.info,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  // Log a warning message
  static void w(
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _instance._log(
      logger_lib.Level.warning,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  // Log an error message
  static void e(
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _instance._log(
      logger_lib.Level.error,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  // Log a WTF (What a Terrible Failure) message
  static void wtf(
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _instance._log(
      logger_lib.Level.wtf,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  // Internal method to handle logging
  void _log(
    logger_lib.Level level,
    dynamic message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    if (!_enabled) return;
    
    try {
      final formattedMessage = tag != null ? '[$tag] $message' : message.toString();
      
      // Log to console in debug mode
      if (kDebugMode) {
        _logger.log(level, formattedMessage, error: error, stackTrace: stackTrace);
      } else {
        // In release mode, only log errors and warnings
        if (level == logger_lib.Level.error || level == logger_lib.Level.wtf) {
          developer.log(
            formattedMessage,
            level: level.value,
            error: error,
            stackTrace: stackTrace,
            name: 'GaadiSewa',
          );
        }
      }
    } catch (e) {
      // Fallback to print if logging fails
      print('Logger error: $e');
      print('Original message: $message');
    }
  }

  // Log API request
  static void apiRequest({
    required String method,
    required String url,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) {
    if (!_instance._enabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('\n╔══════════════════════════════════════════════════════════════');
    buffer.writeln('║ 🌐 API Request: $method $url');
    
    if (queryParameters != null && queryParameters.isNotEmpty) {
      buffer.writeln('║ Query Parameters:');
      queryParameters.forEach((key, value) {
        buffer.writeln('║   $key: $value');
      });
    }
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('║ Headers:');
      headers.forEach((key, value) {
        if (key.toLowerCase() != 'authorization') {
          buffer.writeln('║   $key: $value');
        } else {
          buffer.writeln('║   $key: ********');
        }
      });
    }
    
    if (data != null) {
      buffer.writeln('║ Body:');
      if (data is FormData) {
        // Skip binary data in form data
        buffer.writeln('║   [FormData]');
      } else if (data is Map || data is List) {
        final encoder = JsonEncoder.withIndent('  ');
        final prettyJson = encoder.convert(data);
        prettyJson.split('\n').forEach((line) {
          buffer.writeln('║   $line');
        });
      } else {
        buffer.writeln('║   $data');
      }
    }
    
    buffer.writeln('╚══════════════════════════════════════════════════════════════');
    
    _instance._log(logger_lib.Level.debug, buffer.toString());
  }
  
  // Log API response
  static void apiResponse({
    required String method,
    required String url,
    int? statusCode,
    dynamic data,
    Map<String, dynamic>? headers,
  }) {
    if (!_instance._enabled) return;
    
    final buffer = StringBuffer();
    final isError = statusCode != null && statusCode >= 400;
    final emoji = isError ? '❌' : '✅';
    
    buffer.writeln('\n╔══════════════════════════════════════════════════════════════');
    buffer.writeln('║ $emoji API Response: $method $url');
    buffer.writeln('║ Status: $statusCode ${_getStatusMessage(statusCode)}');
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('║ Headers:');
      headers.forEach((key, value) {
        if (key.toLowerCase() != 'authorization') {
          buffer.writeln('║   $key: $value');
        } else {
          buffer.writeln('║   $key: ********');
        }
      });
    }
    
    if (data != null) {
      buffer.writeln('║ Body:');
      if (data is Map || data is List) {
        try {
          final encoder = JsonEncoder.withIndent('  ');
          final prettyJson = encoder.convert(data);
          prettyJson.split('\n').take(50).forEach((line) {
            buffer.writeln('║   $line');
          });
          
          if (prettyJson.split('\n').length > 50) {
            buffer.writeln('║   ... (response truncated)');
          }
        } catch (e) {
          buffer.writeln('║   [Unable to parse response body]');
        }
      } else {
        final dataStr = data.toString();
        if (dataStr.length > 500) {
          buffer.writeln('║   ${dataStr.substring(0, 500)}... (truncated)');
        } else {
          buffer.writeln('║   $dataStr');
        }
      }
    }
    
    buffer.writeln('╚══════════════════════════════════════════════════════════════');
    
    _instance._log(
      isError ? logger_lib.Level.error : logger_lib.Level.debug,
      buffer.toString(),
    );
  }
  
  // Log API error
  static void apiError({
    required String method,
    required String url,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    if (!_instance._enabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('\n╔══════════════════════════════════════════════════════════════');
    buffer.writeln('║ ❌ API Error: $method $url');
    buffer.writeln('║ Error: $error');
    
    if (stackTrace != null) {
      buffer.writeln('║ Stack Trace:');
      buffer.writeln('║   ${stackTrace.toString().replaceAll('\n', '\n║   ')}');
    }
    
    buffer.writeln('╚══════════════════════════════════════════════════════════════');
    
    _instance._log(
      logger_lib.Level.error,
      buffer.toString(),
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  // Get HTTP status message
  static String _getStatusMessage(int? statusCode) {
    if (statusCode == null) return 'Unknown';
    
    switch (statusCode) {
      case 200:
        return 'OK';
      case 201:
        return 'Created';
      case 204:
        return 'No Content';
      case 400:
        return 'Bad Request';
      case 401:
        return 'Unauthorized';
      case 403:
        return 'Forbidden';
      case 404:
        return 'Not Found';
      case 405:
        return 'Method Not Allowed';
      case 408:
        return 'Request Timeout';
      case 409:
        return 'Conflict';
      case 422:
        return 'Unprocessable Entity';
      case 429:
        return 'Too Many Requests';
      case 500:
        return 'Internal Server Error';
      case 502:
        return 'Bad Gateway';
      case 503:
        return 'Service Unavailable';
      case 504:
        return 'Gateway Timeout';
      default:
        return 'Status Code: $statusCode';
    }
  }
}

/// Extension methods for the Logger class
extension LoggerExtension on Object {
  /// Log a verbose message
  void logV(dynamic message, {String? tag}) {
    Logger.v(message, tag: tag ?? runtimeType.toString());
  }
  
  /// Log a debug message
  void logD(dynamic message, {String? tag}) {
    Logger.d(message, tag: tag ?? runtimeType.toString());
  }
  
  /// Log an info message
  void logI(dynamic message, {String? tag}) {
    Logger.i(message, tag: tag ?? runtimeType.toString());
  }
  
  /// Log a warning message
  void logW(dynamic message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    Logger.w(
      message, 
      tag: tag ?? runtimeType.toString(),
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// Log an error message
  void logE(dynamic message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    Logger.e(
      message, 
      tag: tag ?? runtimeType.toString(),
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// Log a WTF (What a Terrible Failure) message
  void logWtf(dynamic message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    Logger.wtf(
      message, 
      tag: tag ?? runtimeType.toString(),
      error: error,
      stackTrace: stackTrace,
    );
  }
}
