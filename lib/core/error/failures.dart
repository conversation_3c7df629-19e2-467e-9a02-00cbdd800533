import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final String? code;
  final StackTrace? stackTrace;

  const Failure({
    required this.message,
    this.code,
    this.stackTrace,
  });

  @override
  List<Object?> get props => [message, code, stackTrace];

  @override
  bool? get stringify => true;
}

// Common failures
class ServerFailure extends Failure {
  final int? statusCode;
  final String? error;
  
  const ServerFailure({
    required String message,
    this.statusCode,
    this.error,
    String? code,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          code: code,
          stackTrace: stackTrace,
        );
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required String message,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class CacheFailure extends Failure {
  const CacheFailure({
    required String message,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class ValidationFailure extends Failure {
  final Map<String, List<String>>? errors;

  const ValidationFailure({
    required String message,
    this.errors,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );

  @override
  List<Object?> get props => [message, errors, stackTrace];
}

class UnauthorizedFailure extends Failure {
  const UnauthorizedFailure({
    String message = 'Unauthorized access. Please log in again.',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class NotFoundFailure extends Failure {
  const NotFoundFailure({
    String message = 'The requested resource was not found.',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class NoInternetFailure extends Failure {
  const NoInternetFailure({
    String message = 'No internet connection. Please check your connection and try again.',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class TimeoutFailure extends Failure {
  const TimeoutFailure({
    String message = 'The request timed out. Please try again later.',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class UnknownFailure extends Failure {
  const UnknownFailure({
    String message = 'An unknown error occurred. Please try again later.',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class AuthFailure extends Failure {
  const AuthFailure({
    required String message,
    String? code,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          code: code,
          stackTrace: stackTrace,
        );
}

class InvalidInputFailure extends Failure {
  const InvalidInputFailure({
    required String message,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}
