import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/rating_stats_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ReviewRepositoryImpl implements ReviewRepository {
  final NetworkInfo networkInfo;
  final SupabaseClient supabaseClient;

  ReviewRepositoryImpl({
    required this.networkInfo,
    required this.supabaseClient,
  });

  @override
  Future<Either<Failure, List<ReviewModel>>> getVehicleReviews(String vehicleId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('reviews')
          .select('*, profiles:user_id(*)')
          .eq('vehicle_id', vehicleId)
          .order('created_at', ascending: false);

      final reviews = (response as List)
          .map((json) => ReviewModel.fromJson(json as Map<String, dynamic>))
          .toList();

      return Right(reviews);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ReviewModel>>> getUserReviews(String userId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('reviews')
          .select('*, vehicles:vehicle_id(*)')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final reviews = (response as List)
          .map((json) => ReviewModel.fromJson(json as Map<String, dynamic>))
          .toList();

      return Right(reviews);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, ReviewModel>> getReview(String reviewId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('reviews')
          .select('*, profiles:user_id(*), vehicles:vehicle_id(*)')
          .eq('id', reviewId)
          .single();

      final review = ReviewModel.fromJson(response);
      return Right(review);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure(message: 'Review not found'));
      }
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, ReviewModel>> addReview({
    required String vehicleId,
    required String userId,
    String? bookingId,
    required double rating,
    required String comment,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Check if the user has already reviewed this vehicle
      final existingReview = await supabaseClient
          .from('reviews')
          .select('id')
          .match({'user_id': userId, 'vehicle_id': vehicleId});

      if (existingReview.isNotEmpty) {
        return Left(InvalidInputFailure(
            message: 'You have already reviewed this vehicle'));
      }

      // Create the review
      final reviewData = {
        'vehicle_id': vehicleId,
        'user_id': userId,
        if (bookingId != null) 'booking_id': bookingId,
        'rating': rating,
        'comment': comment,
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('reviews')
          .insert(reviewData)
          .select('*, profiles:user_id(*)')
          .single();

      final newReview = ReviewModel.fromJson(response);

      // Update the rating statistics
      await _updateVehicleRatingStats(vehicleId, rating, null);

      return Right(newReview);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, ReviewModel>> updateReview({
    required String reviewId,
    double? rating,
    String? comment,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Get the current review to compare ratings
      final currentReviewResponse = await supabaseClient
          .from('reviews')
          .select('rating, vehicle_id')
          .eq('id', reviewId)
          .single();

      final oldRating = (currentReviewResponse['rating'] as num).toDouble();
      final vehicleId = currentReviewResponse['vehicle_id'] as String;

      // Update the review
      final updateData = {
        if (rating != null) 'rating': rating,
        if (comment != null) 'comment': comment,
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('reviews')
          .update(updateData)
          .eq('id', reviewId)
          .select('*, profiles:user_id(*)')
          .single();

      final updatedReview = ReviewModel.fromJson(response);

      // Update the rating statistics if the rating changed
      if (rating != null && rating != oldRating) {
        await _updateVehicleRatingStats(vehicleId, rating, oldRating);
      }

      return Right(updatedReview);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure(message: 'Review not found'));
      }
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteReview(String reviewId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Get the review details before deletion
      final reviewResponse = await supabaseClient
          .from('reviews')
          .select('rating, vehicle_id')
          .eq('id', reviewId)
          .single();

      final rating = (reviewResponse['rating'] as num).toDouble();
      final vehicleId = reviewResponse['vehicle_id'] as String;

      // Delete the review
      await supabaseClient.from('reviews').delete().eq('id', reviewId);

      // Update the rating statistics
      await _updateVehicleRatingStats(vehicleId, null, rating);

      return const Right(true);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, RatingStatsModel>> getVehicleRatingStats(
      String vehicleId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('rating_stats')
          .select()
          .eq('vehicle_id', vehicleId)
          .maybeSingle();

      if (response == null) {
        // If no stats exist yet, return empty stats
        return Right(RatingStatsModel.empty(vehicleId));
      }

      final stats = RatingStatsModel.fromJson(response);
      return Right(stats);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasUserReviewedVehicle({
    required String userId,
    required String vehicleId,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('reviews')
          .select('id')
          .match({'user_id': userId, 'vehicle_id': vehicleId});

      return Right(response.isNotEmpty);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  /// Helper method to update the vehicle rating statistics
  Future<void> _updateVehicleRatingStats(
      String vehicleId, double? newRating, double? oldRating) async {
    try {
      // Get current stats
      final statsResponse = await supabaseClient
          .from('rating_stats')
          .select()
          .eq('vehicle_id', vehicleId)
          .maybeSingle();

      RatingStatsModel stats;
      if (statsResponse == null) {
        // Create new stats if none exist
        stats = RatingStatsModel.empty(vehicleId);
      } else {
        stats = RatingStatsModel.fromJson(statsResponse);
      }

      // Update the stats based on the operation
      if (newRating != null && oldRating == null) {
        // Adding a new rating
        stats = stats.addRating(newRating);
      } else if (newRating != null && oldRating != null) {
        // Updating an existing rating
        stats = stats.updateRating(oldRating, newRating);
      } else if (newRating == null && oldRating != null) {
        // Removing a rating
        stats = stats.removeRating(oldRating);
      }

      // Upsert the updated stats
      await supabaseClient
          .from('rating_stats')
          .upsert(stats.toJson())
          .select()
          .single();
    } catch (e) {
      // Log error but don't fail the main operation
      print('Error updating rating stats: $e');
    }
  }
}
