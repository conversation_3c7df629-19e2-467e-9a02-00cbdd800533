import 'package:flutter/material.dart';

class RatingBar extends StatelessWidget {
  final double rating;
  final double size;
  final bool isInteractive;
  final ValueChanged<double>? onRatingChanged;
  final Color? activeColor;
  final Color? inactiveColor;

  const RatingBar({
    Key? key,
    required this.rating,
    this.size = 24,
    this.isInteractive = false,
    this.onRatingChanged,
    this.activeColor,
    this.inactiveColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final activeStarColor = activeColor ?? Colors.amber;
    final inactiveStarColor = inactiveColor ?? Colors.grey[300];

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1;
        return GestureDetector(
          onTap: isInteractive
              ? () => onRatingChanged?.call(starValue.toDouble())
              : null,
          child: Icon(
            starValue <= rating
                ? Icons.star
                : (starValue - rating) < 1 && (starValue - rating) > 0
                    ? Icons.star_half
                    : Icons.star_border,
            color: starValue <= rating
                ? activeStarColor
                : (starValue - rating) < 1 && (starValue - rating) > 0
                    ? activeStarColor
                    : inactiveStarColor,
            size: size,
          ),
        );
      }),
    );
  }
}

class RatingInputBar extends StatefulWidget {
  final double initialRating;
  final ValueChanged<double> onRatingChanged;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;

  const RatingInputBar({
    Key? key,
    this.initialRating = 0,
    required this.onRatingChanged,
    this.size = 36,
    this.activeColor,
    this.inactiveColor,
  }) : super(key: key);

  @override
  State<RatingInputBar> createState() => _RatingInputBarState();
}

class _RatingInputBarState extends State<RatingInputBar> {
  late double _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    final activeStarColor = widget.activeColor ?? Colors.amber;
    final inactiveStarColor = widget.inactiveColor ?? Colors.grey[300];

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1;
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentRating = starValue.toDouble();
            });
            widget.onRatingChanged(_currentRating);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Icon(
              starValue <= _currentRating ? Icons.star : Icons.star_border,
              color: starValue <= _currentRating
                  ? activeStarColor
                  : inactiveStarColor,
              size: widget.size,
            ),
          ),
        );
      }),
    );
  }
}
