import 'package:flutter/material.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/rating_stats_model.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/rating_bar.dart';

class RatingStatsWidget extends StatelessWidget {
  final RatingStatsModel stats;
  final bool showRatingCount;
  final bool compact;

  const RatingStatsWidget({
    Key? key,
    required this.stats,
    this.showRatingCount = true,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              stats.averageRating.toStringAsFixed(1),
              style: TextStyle(
                fontSize: compact ? 24 : 36,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RatingBar(
                  rating: stats.averageRating,
                  size: compact ? 16 : 20,
                ),
                if (showRatingCount)
                  Text(
                    '${stats.totalReviews} ${stats.totalReviews == 1 ? 'review' : 'reviews'}',
                    style: TextStyle(
                      fontSize: compact ? 12 : 14,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ],
        ),
        if (!compact) ...[
          const SizedBox(height: 16),
          ...List.generate(5, (index) {
            final ratingValue = 5 - index;
            final ratingKey = ratingValue.toString();
            final count = stats.ratingDistribution[ratingKey] ?? 0;
            final percentage = stats.totalReviews > 0
                ? count / stats.totalReviews
                : 0.0;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  SizedBox(
                    width: 12,
                    child: Text(
                      '$ratingValue',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: LinearProgressIndicator(
                        value: percentage,
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getColorForRating(ratingValue, context),
                        ),
                        minHeight: 8,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 24,
                    child: Text(
                      count.toString(),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ],
    );
  }

  Color _getColorForRating(int rating, BuildContext context) {
    switch (rating) {
      case 5:
        return Colors.green;
      case 4:
        return Colors.lightGreen;
      case 3:
        return Colors.amber;
      case 2:
        return Colors.orange;
      case 1:
        return Colors.red;
      default:
        return Theme.of(context).primaryColor;
    }
  }
}
