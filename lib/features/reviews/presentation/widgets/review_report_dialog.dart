import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';

enum ReportReason {
  inappropriate,
  spam,
  offensive,
  misleading,
  other,
}

class ReviewReportDialog extends ConsumerStatefulWidget {
  final ReviewModel review;

  const ReviewReportDialog({
    Key? key,
    required this.review,
  }) : super(key: key);

  @override
  ConsumerState<ReviewReportDialog> createState() => _ReviewReportDialogState();
}

class _ReviewReportDialogState extends ConsumerState<ReviewReportDialog> {
  ReportReason? _selectedReason;
  final TextEditingController _additionalInfoController = TextEditingController();
  bool _isSubmitting = false;
  String? _error;

  @override
  void dispose() {
    _additionalInfoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: const Text('Report Review'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Why are you reporting this review?',
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            ...ReportReason.values.map((reason) => _buildReasonRadio(reason)),
            const SizedBox(height: 16),
            TextField(
              controller: _additionalInfoController,
              decoration: const InputDecoration(
                labelText: 'Additional information (optional)',
                hintText: 'Please provide more details',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            if (_error != null)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Text(
                  _error!,
                  style: TextStyle(
                    color: theme.colorScheme.error,
                  ),
                ),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isSubmitting
              ? null
              : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isSubmitting ? null : _submitReport,
          child: _isSubmitting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                )
              : const Text('Submit Report'),
        ),
      ],
    );
  }

  Widget _buildReasonRadio(ReportReason reason) {
    return RadioListTile<ReportReason>(
      title: Text(_getReasonText(reason)),
      value: reason,
      groupValue: _selectedReason,
      onChanged: (ReportReason? value) {
        setState(() {
          _selectedReason = value;
        });
      },
    );
  }

  String _getReasonText(ReportReason reason) {
    switch (reason) {
      case ReportReason.inappropriate:
        return 'Inappropriate content';
      case ReportReason.spam:
        return 'Spam or commercial';
      case ReportReason.offensive:
        return 'Offensive language';
      case ReportReason.misleading:
        return 'Misleading information';
      case ReportReason.other:
        return 'Other reason';
    }
  }

  Future<void> _submitReport() async {
    if (_selectedReason == null) {
      setState(() {
        _error = 'Please select a reason for reporting';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
      _error = null;
    });

    try {
      // TODO: Implement actual API call to report the review
      // For now, just simulate a network delay
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;

      Navigator.of(context).pop();
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Review reported successfully. Our team will review it.'),
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      setState(() {
        _isSubmitting = false;
        _error = 'Failed to submit report: $e';
      });
    }
  }
}

void showReviewReportDialog(BuildContext context, ReviewModel review) {
  showDialog(
    context: context,
    builder: (context) => ReviewReportDialog(review: review),
  );
}
