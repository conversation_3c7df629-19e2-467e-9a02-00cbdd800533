import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum ReviewSortOption {
  newest,
  oldest,
  highestRating,
  lowestRating,
  mostHelpful,
}

class ReviewFilterState {
  final ReviewSortOption sortOption;
  final Set<int> ratingFilters;
  final bool onlyWithComments;

  ReviewFilterState({
    this.sortOption = ReviewSortOption.newest,
    this.ratingFilters = const {},
    this.onlyWithComments = false,
  });

  ReviewFilterState copyWith({
    ReviewSortOption? sortOption,
    Set<int>? ratingFilters,
    bool? onlyWithComments,
  }) {
    return ReviewFilterState(
      sortOption: sortOption ?? this.sortOption,
      ratingFilters: ratingFilters ?? this.ratingFilters,
      onlyWithComments: onlyWithComments ?? this.onlyWithComments,
    );
  }
}

class ReviewFilterNotifier extends StateNotifier<ReviewFilterState> {
  ReviewFilterNotifier() : super(ReviewFilterState());

  void setSortOption(ReviewSortOption option) {
    state = state.copyWith(sortOption: option);
  }

  void toggleRatingFilter(int rating) {
    final newFilters = Set<int>.from(state.ratingFilters);
    if (newFilters.contains(rating)) {
      newFilters.remove(rating);
    } else {
      newFilters.add(rating);
    }
    state = state.copyWith(ratingFilters: newFilters);
  }

  void clearRatingFilters() {
    state = state.copyWith(ratingFilters: {});
  }

  void toggleOnlyWithComments() {
    state = state.copyWith(onlyWithComments: !state.onlyWithComments);
  }

  void resetFilters() {
    state = ReviewFilterState();
  }
}

final reviewFilterProvider =
    StateNotifierProvider<ReviewFilterNotifier, ReviewFilterState>((ref) {
  return ReviewFilterNotifier();
});

class ReviewFilterWidget extends ConsumerWidget {
  const ReviewFilterWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterState = ref.watch(reviewFilterProvider);
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter Reviews',
                style: theme.textTheme.titleLarge,
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Sort by',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildSortChip(
                context,
                ref,
                ReviewSortOption.newest,
                'Newest',
              ),
              _buildSortChip(
                context,
                ref,
                ReviewSortOption.oldest,
                'Oldest',
              ),
              _buildSortChip(
                context,
                ref,
                ReviewSortOption.highestRating,
                'Highest Rating',
              ),
              _buildSortChip(
                context,
                ref,
                ReviewSortOption.lowestRating,
                'Lowest Rating',
              ),
              _buildSortChip(
                context,
                ref,
                ReviewSortOption.mostHelpful,
                'Most Helpful',
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter by rating',
                style: theme.textTheme.titleMedium,
              ),
              if (filterState.ratingFilters.isNotEmpty)
                TextButton(
                  onPressed: () {
                    ref
                        .read(reviewFilterProvider.notifier)
                        .clearRatingFilters();
                  },
                  child: const Text('Clear'),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(5, (index) {
              final rating = index + 1;
              return _buildRatingFilterChip(context, ref, rating);
            }),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Only show reviews with comments'),
            value: filterState.onlyWithComments,
            onChanged: (value) {
              ref.read(reviewFilterProvider.notifier).toggleOnlyWithComments();
            },
            contentPadding: EdgeInsets.zero,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              OutlinedButton(
                onPressed: () {
                  ref.read(reviewFilterProvider.notifier).resetFilters();
                },
                child: const Text('Reset All'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Apply Filters'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortChip(
    BuildContext context,
    WidgetRef ref,
    ReviewSortOption option,
    String label,
  ) {
    final filterState = ref.watch(reviewFilterProvider);
    final isSelected = filterState.sortOption == option;
    final theme = Theme.of(context);

    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          ref.read(reviewFilterProvider.notifier).setSortOption(option);
        }
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      selectedColor: theme.colorScheme.primaryContainer,
    );
  }

  Widget _buildRatingFilterChip(
    BuildContext context,
    WidgetRef ref,
    int rating,
  ) {
    final filterState = ref.watch(reviewFilterProvider);
    final isSelected = filterState.ratingFilters.contains(rating);
    final theme = Theme.of(context);

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('$rating'),
          const SizedBox(width: 2),
          Icon(
            Icons.star,
            size: 16,
            color: isSelected
                ? theme.colorScheme.onPrimaryContainer
                : theme.colorScheme.onSurfaceVariant,
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(reviewFilterProvider.notifier).toggleRatingFilter(rating);
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      selectedColor: theme.colorScheme.primaryContainer,
    );
  }
}

void showReviewFilterBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: const ReviewFilterWidget(),
    ),
  );
}
