import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/reviews/data/repositories/review_repository_impl.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/rating_stats_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/add_review.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/delete_review.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/get_user_reviews.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/get_vehicle_rating_stats.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/get_vehicle_reviews.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/has_user_reviewed_vehicle.dart';
import 'package:gaadi_sewa/features/reviews/domain/usecases/update_review.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Repository provider
final reviewRepositoryProvider = Provider<ReviewRepository>((ref) {
  return ReviewRepositoryImpl(
    networkInfo: ref.read(networkInfoProvider),
    supabaseClient: Supabase.instance.client,
  );
});

// Use case providers
final getVehicleReviewsProvider = Provider<GetVehicleReviews>((ref) {
  return GetVehicleReviews(ref.read(reviewRepositoryProvider));
});

final getUserReviewsProvider = Provider<GetUserReviews>((ref) {
  return GetUserReviews(ref.read(reviewRepositoryProvider));
});

final addReviewProvider = Provider<AddReview>((ref) {
  return AddReview(ref.read(reviewRepositoryProvider));
});

final updateReviewProvider = Provider<UpdateReview>((ref) {
  return UpdateReview(ref.read(reviewRepositoryProvider));
});

final deleteReviewProvider = Provider<DeleteReview>((ref) {
  return DeleteReview(ref.read(reviewRepositoryProvider));
});

final getVehicleRatingStatsProvider = Provider<GetVehicleRatingStats>((ref) {
  return GetVehicleRatingStats(ref.read(reviewRepositoryProvider));
});

final hasUserReviewedVehicleProvider = Provider<HasUserReviewedVehicle>((ref) {
  return HasUserReviewedVehicle(ref.read(reviewRepositoryProvider));
});

// State classes
class ReviewsState {
  final List<ReviewModel>? reviews;
  final bool isLoading;
  final Failure? failure;

  ReviewsState({
    this.reviews,
    this.isLoading = false,
    this.failure,
  });

  ReviewsState copyWith({
    List<ReviewModel>? reviews,
    bool? isLoading,
    Failure? failure,
  }) {
    return ReviewsState(
      reviews: reviews ?? this.reviews,
      isLoading: isLoading ?? this.isLoading,
      failure: failure,
    );
  }
}

class RatingStatsState {
  final RatingStatsModel? stats;
  final bool isLoading;
  final Failure? failure;

  RatingStatsState({
    this.stats,
    this.isLoading = false,
    this.failure,
  });

  RatingStatsState copyWith({
    RatingStatsModel? stats,
    bool? isLoading,
    Failure? failure,
  }) {
    return RatingStatsState(
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      failure: failure,
    );
  }
}

// Vehicle reviews state notifier
class VehicleReviewsNotifier extends StateNotifier<ReviewsState> {
  final GetVehicleReviews _getVehicleReviews;
  final AddReview _addReview;
  final UpdateReview _updateReview;
  final DeleteReview _deleteReview;

  VehicleReviewsNotifier({
    required GetVehicleReviews getVehicleReviews,
    required AddReview addReview,
    required UpdateReview updateReview,
    required DeleteReview deleteReview,
  })  : _getVehicleReviews = getVehicleReviews,
        _addReview = addReview,
        _updateReview = updateReview,
        _deleteReview = deleteReview,
        super(ReviewsState());

  Future<void> getVehicleReviews(String vehicleId) async {
    state = state.copyWith(isLoading: true, failure: null);
    final result = await _getVehicleReviews(GetVehicleReviewsParams(vehicleId: vehicleId));
    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (reviews) => state = state.copyWith(isLoading: false, reviews: reviews),
    );
  }

  Future<bool> addReview({
    required String vehicleId,
    required String userId,
    String? bookingId,
    required double rating,
    required String comment,
  }) async {
    state = state.copyWith(isLoading: true, failure: null);
    final params = AddReviewParams(
      vehicleId: vehicleId,
      userId: userId,
      bookingId: bookingId,
      rating: rating,
      comment: comment,
    );
    
    final result = await _addReview(params);
    return result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, failure: failure);
        return false;
      },
      (review) {
        // Add the new review to the list if we already have reviews loaded
        if (state.reviews != null) {
          final updatedReviews = [review, ...state.reviews!];
          state = state.copyWith(isLoading: false, reviews: updatedReviews);
        } else {
          state = state.copyWith(isLoading: false);
        }
        return true;
      },
    );
  }

  Future<bool> updateReview({
    required String reviewId,
    double? rating,
    String? comment,
  }) async {
    state = state.copyWith(isLoading: true, failure: null);
    final params = UpdateReviewParams(
      reviewId: reviewId,
      rating: rating,
      comment: comment,
    );
    
    final result = await _updateReview(params);
    return result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, failure: failure);
        return false;
      },
      (updatedReview) {
        // Update the review in the list if we have reviews loaded
        if (state.reviews != null) {
          final updatedReviews = state.reviews!.map((review) {
            return review.id == updatedReview.id ? updatedReview : review;
          }).toList();
          state = state.copyWith(isLoading: false, reviews: updatedReviews);
        } else {
          state = state.copyWith(isLoading: false);
        }
        return true;
      },
    );
  }

  Future<bool> deleteReview(String reviewId) async {
    state = state.copyWith(isLoading: true, failure: null);
    final params = DeleteReviewParams(reviewId: reviewId);
    
    final result = await _deleteReview(params);
    return result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, failure: failure);
        return false;
      },
      (_) {
        // Remove the review from the list if we have reviews loaded
        if (state.reviews != null) {
          final updatedReviews = state.reviews!
              .where((review) => review.id != reviewId)
              .toList();
          state = state.copyWith(isLoading: false, reviews: updatedReviews);
        } else {
          state = state.copyWith(isLoading: false);
        }
        return true;
      },
    );
  }
}

// User reviews state notifier
class UserReviewsNotifier extends StateNotifier<ReviewsState> {
  final GetUserReviews _getUserReviews;

  UserReviewsNotifier({
    required GetUserReviews getUserReviews,
  })  : _getUserReviews = getUserReviews,
        super(ReviewsState());

  Future<void> getUserReviews(String userId) async {
    state = state.copyWith(isLoading: true, failure: null);
    final result = await _getUserReviews(GetUserReviewsParams(userId: userId));
    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (reviews) => state = state.copyWith(isLoading: false, reviews: reviews),
    );
  }
}

// Rating stats state notifier
class RatingStatsNotifier extends StateNotifier<RatingStatsState> {
  final GetVehicleRatingStats _getVehicleRatingStats;

  RatingStatsNotifier({
    required GetVehicleRatingStats getVehicleRatingStats,
  })  : _getVehicleRatingStats = getVehicleRatingStats,
        super(RatingStatsState());

  Future<void> getVehicleRatingStats(String vehicleId) async {
    state = state.copyWith(isLoading: true, failure: null);
    final result = await _getVehicleRatingStats(
        GetVehicleRatingStatsParams(vehicleId: vehicleId));
    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (stats) => state = state.copyWith(isLoading: false, stats: stats),
    );
  }
}

// Has user reviewed vehicle state
class HasReviewedState {
  final bool? hasReviewed;
  final bool isLoading;
  final Failure? failure;

  HasReviewedState({
    this.hasReviewed,
    this.isLoading = false,
    this.failure,
  });

  HasReviewedState copyWith({
    bool? hasReviewed,
    bool? isLoading,
    Failure? failure,
  }) {
    return HasReviewedState(
      hasReviewed: hasReviewed ?? this.hasReviewed,
      isLoading: isLoading ?? this.isLoading,
      failure: failure,
    );
  }
}

// Has user reviewed vehicle notifier
class HasUserReviewedVehicleNotifier extends StateNotifier<HasReviewedState> {
  final HasUserReviewedVehicle _hasUserReviewedVehicle;

  HasUserReviewedVehicleNotifier({
    required HasUserReviewedVehicle hasUserReviewedVehicle,
  })  : _hasUserReviewedVehicle = hasUserReviewedVehicle,
        super(HasReviewedState());

  Future<void> checkUserReviewed({
    required String userId,
    required String vehicleId,
  }) async {
    state = state.copyWith(isLoading: true, failure: null);
    final params = HasUserReviewedVehicleParams(
      userId: userId,
      vehicleId: vehicleId,
    );
    
    final result = await _hasUserReviewedVehicle(params);
    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (hasReviewed) => state = state.copyWith(isLoading: false, hasReviewed: hasReviewed),
    );
  }
}

// Provider for vehicle reviews
final vehicleReviewsProvider = StateNotifierProvider.family<VehicleReviewsNotifier, ReviewsState, String>((ref, vehicleId) {
  return VehicleReviewsNotifier(
    getVehicleReviews: ref.read(getVehicleReviewsProvider),
    addReview: ref.read(addReviewProvider),
    updateReview: ref.read(updateReviewProvider),
    deleteReview: ref.read(deleteReviewProvider),
  )..getVehicleReviews(vehicleId);
});

// Provider for user reviews
final userReviewsProvider = StateNotifierProvider.family<UserReviewsNotifier, ReviewsState, String>((ref, userId) {
  return UserReviewsNotifier(
    getUserReviews: ref.read(getUserReviewsProvider),
  )..getUserReviews(userId);
});

// Provider for rating stats
final ratingStatsProvider = StateNotifierProvider.family<RatingStatsNotifier, RatingStatsState, String>((ref, vehicleId) {
  return RatingStatsNotifier(
    getVehicleRatingStats: ref.read(getVehicleRatingStatsProvider),
  )..getVehicleRatingStats(vehicleId);
});

// Provider for has user reviewed vehicle
final hasUserReviewedVehicleStateProvider = StateNotifierProvider<HasUserReviewedVehicleNotifier, HasReviewedState>((ref) {
  return HasUserReviewedVehicleNotifier(
    hasUserReviewedVehicle: ref.read(hasUserReviewedVehicleProvider),
  );
});
