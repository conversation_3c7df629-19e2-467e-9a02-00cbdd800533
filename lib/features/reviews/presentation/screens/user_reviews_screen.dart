import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/widgets/app_bar_widget.dart';
import 'package:gaadi_sewa/core/widgets/error_widget.dart';
import 'package:gaadi_sewa/core/widgets/loading_widget.dart';
import 'package:gaadi_sewa/features/auth/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/presentation/providers/review_provider.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_form_dialog.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_item.dart';

class UserReviewsScreen extends ConsumerWidget {
  static const routeName = '/my-reviews';

  const UserReviewsScreen({Key? key}) : super(key: key);

  void _confirmDeleteReview(
    BuildContext context,
    WidgetRef ref,
    ReviewModel review,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Review'),
        content: const Text(
          'Are you sure you want to delete your review? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final success = await ref
                  .read(vehicleReviewsProvider(review.vehicleId).notifier)
                  .deleteReview(review.id);

              if (success) {
                // Refresh user reviews
                final currentUser = ref.read(authProvider);
                if (currentUser != null) {
                  ref.refresh(userReviewsProvider(currentUser.id));
                  
                  // Update the has reviewed state for this vehicle
                  ref.read(hasUserReviewedVehicleStateProvider.notifier).checkUserReviewed(
                    userId: currentUser.id,
                    vehicleId: review.vehicleId,
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showEditReviewDialog(
    BuildContext context,
    WidgetRef ref,
    ReviewModel review,
  ) {
    showDialog(
      context: context,
      builder: (context) => ReviewFormDialog(
        vehicleId: review.vehicleId,
        vehicleName: review.metadata?['vehicleName'] ?? 'Vehicle',
        existingReview: review,
        onSubmit: (rating, comment) async {
          Navigator.of(context).pop();

          final success = await ref
              .read(vehicleReviewsProvider(review.vehicleId).notifier)
              .updateReview(
                reviewId: review.id,
                rating: rating,
                comment: comment,
              );

          if (success) {
            // Refresh user reviews
            final currentUser = ref.read(authProvider);
            if (currentUser != null) {
              ref.refresh(userReviewsProvider(currentUser.id));
            }
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(authProvider);
    
    if (currentUser == null) {
      return Scaffold(
        appBar: const AppBarWidget(title: 'My Reviews'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.account_circle,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Please log in to view your reviews',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    final userReviewsState = ref.watch(userReviewsProvider(currentUser.id));

    return Scaffold(
      appBar: const AppBarWidget(title: 'My Reviews'),
      body: userReviewsState.isLoading
          ? const LoadingWidget()
          : userReviewsState.failure != null
              ? AppErrorWidget(
                  failure: userReviewsState.failure!,
                  onRetry: () => ref.refresh(
                    userReviewsProvider(currentUser.id),
                  ),
                )
              : userReviewsState.reviews == null ||
                      userReviewsState.reviews!.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.rate_review_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'You haven\'t written any reviews yet',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Your reviews will appear here after you rate vehicles',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: userReviewsState.reviews!.length,
                      itemBuilder: (context, index) {
                        final review = userReviewsState.reviews![index];
                        
                        return ReviewItem(
                          review: review,
                          isOwner: true,
                          onEdit: () => _showEditReviewDialog(
                            context,
                            ref,
                            review,
                          ),
                          onDelete: () => _confirmDeleteReview(
                            context,
                            ref,
                            review,
                          ),
                        );
                      },
                    ),
    );
  }
}
