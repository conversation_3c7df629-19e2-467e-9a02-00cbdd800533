import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/widgets/app_bar_widget.dart';
import 'package:gaadi_sewa/core/widgets/error_widget.dart';
import 'package:gaadi_sewa/core/widgets/loading_widget.dart';
import 'package:gaadi_sewa/features/auth/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/presentation/providers/review_provider.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/rating_stats_widget.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_filter_widget.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_form_dialog.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_helpfulness_widget.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_item.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_report_dialog.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

class ReviewListScreen extends ConsumerStatefulWidget {
  static const routeName = '/reviews';

  final VehicleModel vehicle;

  const ReviewListScreen({
    Key? key,
    required this.vehicle,
  }) : super(key: key);

  @override
  ConsumerState<ReviewListScreen> createState() => _ReviewListScreenState();
}

class _ReviewListScreenState extends ConsumerState<ReviewListScreen> {
  @override
  void initState() {
    super.initState();
    // Check if the current user has already reviewed this vehicle
    Future.microtask(() {
      final currentUser = ref.read(authProvider);
      if (currentUser != null) {
        ref.read(hasUserReviewedVehicleStateProvider.notifier).checkUserReviewed(
          userId: currentUser.id,
          vehicleId: widget.vehicle.id,
        );
      }
    });
  }

  void _showReviewDialog({ReviewModel? existingReview}) {
    final currentUser = ref.read(authProvider);
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You need to be logged in to leave a review'),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => ReviewFormDialog(
        vehicleId: widget.vehicle.id,
        vehicleName: '${widget.vehicle.make} ${widget.vehicle.model}',
        existingReview: existingReview,
        onSubmit: (rating, comment) async {
          Navigator.of(context).pop();

          final reviewsNotifier = ref.read(
            vehicleReviewsProvider(widget.vehicle.id).notifier,
          );

          bool success;
          if (existingReview == null) {
            // Add new review
            success = await reviewsNotifier.addReview(
              vehicleId: widget.vehicle.id,
              userId: currentUser.id,
              rating: rating,
              comment: comment,
            );

            if (success) {
              // Update the has reviewed state
              ref.read(hasUserReviewedVehicleStateProvider.notifier).checkUserReviewed(
                userId: currentUser.id,
                vehicleId: widget.vehicle.id,
              );
            }
          } else {
            // Update existing review
            success = await reviewsNotifier.updateReview(
              reviewId: existingReview.id,
              rating: rating,
              comment: comment,
            );
          }

          if (success && mounted) {
            // Refresh rating stats
            ref.refresh(ratingStatsProvider(widget.vehicle.id));
          }
        },
      ),
    );
  }

  void _confirmDeleteReview(ReviewModel review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Review'),
        content: const Text(
          'Are you sure you want to delete your review? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final success = await ref
                  .read(vehicleReviewsProvider(widget.vehicle.id).notifier)
                  .deleteReview(review.id);

              if (success && mounted) {
                // Update the has reviewed state
                final currentUser = ref.read(authProvider);
                if (currentUser != null) {
                  ref.read(hasUserReviewedVehicleStateProvider.notifier).checkUserReviewed(
                    userId: currentUser.id,
                    vehicleId: widget.vehicle.id,
                  );
                }

                // Refresh rating stats
                ref.refresh(ratingStatsProvider(widget.vehicle.id));
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // Helper method to filter and sort reviews based on filter state
  List<ReviewModel> _filterAndSortReviews(
    List<ReviewModel> reviews,
    ReviewFilterState filterState,
  ) {
    // Apply rating filters
    var filteredReviews = reviews;
    if (filterState.ratingFilters.isNotEmpty) {
      filteredReviews = reviews.where((review) {
        return filterState.ratingFilters.contains(review.rating.round());
      }).toList();
    }

    // Apply comment filter
    if (filterState.onlyWithComments) {
      filteredReviews = filteredReviews.where((review) {
        return review.comment.trim().isNotEmpty;
      }).toList();
    }

    // Apply sorting
    filteredReviews.sort((a, b) {
      switch (filterState.sortOption) {
        case ReviewSortOption.newest:
          return b.createdAt.compareTo(a.createdAt);
        case ReviewSortOption.oldest:
          return a.createdAt.compareTo(b.createdAt);
        case ReviewSortOption.highestRating:
          return b.rating.compareTo(a.rating);
        case ReviewSortOption.lowestRating:
          return a.rating.compareTo(b.rating);
        case ReviewSortOption.mostHelpful:
          // Sort by helpful count from metadata
          final aHelpfulCount = (a.metadata?['helpfulCount'] as int?) ?? 0;
          final bHelpfulCount = (b.metadata?['helpfulCount'] as int?) ?? 0;
          return bHelpfulCount.compareTo(aHelpfulCount);
      }
    });

    return filteredReviews;
  }

  // Helper method to get display text for sort options
  String _getSortOptionText(ReviewSortOption option) {
    switch (option) {
      case ReviewSortOption.newest:
        return 'Newest';
      case ReviewSortOption.oldest:
        return 'Oldest';
      case ReviewSortOption.highestRating:
        return 'Highest Rating';
      case ReviewSortOption.lowestRating:
        return 'Lowest Rating';
      case ReviewSortOption.mostHelpful:
        return 'Most Helpful';
    }
  }

  @override
  Widget build(BuildContext context) {
    final reviewsState = ref.watch(vehicleReviewsProvider(widget.vehicle.id));
    final ratingStatsState = ref.watch(ratingStatsProvider(widget.vehicle.id));
    final hasReviewedState = ref.watch(hasUserReviewedVehicleStateProvider);
    final currentUser = ref.watch(authProvider);

    return Scaffold(
      appBar: const AppBarWidget(title: 'Reviews & Ratings'),
      body: reviewsState.isLoading || ratingStatsState.isLoading
          ? const LoadingWidget()
          : reviewsState.failure != null
              ? AppErrorWidget(
                  failure: reviewsState.failure!,
                  onRetry: () => ref.refresh(
                    vehicleReviewsProvider(widget.vehicle.id),
                  ),
                )
              : Column(
                  children: [
                    // Rating stats section
                    Container(
                      padding: const EdgeInsets.all(16),
                      color: Colors.white,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${widget.vehicle.make} ${widget.vehicle.model}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          if (ratingStatsState.stats != null)
                            RatingStatsWidget(
                              stats: ratingStatsState.stats!,
                            ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Reviews (${reviewsState.reviews?.length ?? 0})',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Row(
                                children: [
                                  // Filter button
                                  IconButton(
                                    icon: const Icon(Icons.filter_list),
                                    tooltip: 'Filter reviews',
                                    onPressed: () {
                                      showReviewFilterBottomSheet(context);
                                    },
                                  ),
                                  const SizedBox(width: 8),
                                  // Write review button
                                  if (currentUser != null &&
                                      hasReviewedState.hasReviewed == false)
                                    ElevatedButton.icon(
                                      onPressed: () => _showReviewDialog(),
                                      icon: const Icon(Icons.rate_review),
                                      label: const Text('Write a Review'),
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 8,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                          // Active filters display
                          Consumer(builder: (context, ref, child) {
                            final filterState = ref.watch(reviewFilterProvider);
                            if (filterState.ratingFilters.isEmpty && 
                                filterState.sortOption == ReviewSortOption.newest &&
                                !filterState.onlyWithComments) {
                              return const SizedBox.shrink();
                            }
                            
                            return Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Wrap(
                                spacing: 8,
                                runSpacing: 4,
                                children: [
                                  if (filterState.sortOption != ReviewSortOption.newest)
                                    Chip(
                                      label: Text(_getSortOptionText(filterState.sortOption)),
                                      deleteIcon: const Icon(Icons.close, size: 16),
                                      onDeleted: () {
                                        ref.read(reviewFilterProvider.notifier).setSortOption(ReviewSortOption.newest);
                                      },
                                    ),
                                  ...filterState.ratingFilters.map((rating) => 
                                    Chip(
                                      label: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text('$rating'),
                                          const SizedBox(width: 4),
                                          const Icon(Icons.star, size: 16),
                                        ],
                                      ),
                                      deleteIcon: const Icon(Icons.close, size: 16),
                                      onDeleted: () {
                                        ref.read(reviewFilterProvider.notifier).toggleRatingFilter(rating);
                                      },
                                    ),
                                  ),
                                  if (filterState.onlyWithComments)
                                    Chip(
                                      label: const Text('With comments'),
                                      deleteIcon: const Icon(Icons.close, size: 16),
                                      onDeleted: () {
                                        ref.read(reviewFilterProvider.notifier).toggleOnlyWithComments();
                                      },
                                    ),
                                ],
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                    // Reviews list
                    Expanded(
                      child: reviewsState.reviews == null ||
                              reviewsState.reviews!.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.rate_review_outlined,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No reviews yet',
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  if (currentUser != null &&
                                      hasReviewedState.hasReviewed == false)
                                    ElevatedButton(
                                      onPressed: () => _showReviewDialog(),
                                      child: const Text('Be the first to review'),
                                    ),
                                ],
                              ),
                            )
                          : Consumer(builder: (context, ref, child) {
                              final filterState = ref.watch(reviewFilterProvider);
                              final reviews = _filterAndSortReviews(
                                reviewsState.reviews!,
                                filterState,
                              );
                              
                              if (reviews.isEmpty) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.filter_list,
                                        size: 64,
                                        color: Colors.grey[400],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'No reviews match your filters',
                                        style: TextStyle(
                                          fontSize: 18,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      TextButton(
                                        onPressed: () {
                                          ref.read(reviewFilterProvider.notifier).resetFilters();
                                        },
                                        child: const Text('Clear all filters'),
                                      ),
                                    ],
                                  ),
                                );
                              }
                              
                              return ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: reviews.length,
                                itemBuilder: (context, index) {
                                  final review = reviews[index];
                                  final isOwner = currentUser != null &&
                                      review.userId == currentUser.id;

                                  return Column(
                                    children: [
                                      ReviewItem(
                                        review: review,
                                        isOwner: isOwner,
                                        onEdit: isOwner
                                            ? () => _showReviewDialog(
                                                  existingReview: review,
                                                )
                                            : null,
                                        onDelete: isOwner
                                            ? () => _confirmDeleteReview(review)
                                            : null,
                                        onReport: !isOwner
                                            ? () => showReviewReportDialog(context, review)
                                            : null,
                                      ),
                                      if (!isOwner) // Only show helpfulness widget for other people's reviews
                                        Padding(
                                          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                                          child: ReviewHelpfulnessWidget(review: review),
                                        ),
                                      if (index < reviews.length - 1)
                                        const Divider(),
                                    ],
                                  );
                                },
                              );
                            }),
                    ),
                  ],
                ),
    );
  }
}
