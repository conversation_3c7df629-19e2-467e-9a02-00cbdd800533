import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rating_stats_model.g.dart';

@JsonSerializable()
class RatingStatsModel extends Equatable {
  final String vehicleId;
  final double averageRating;
  final int totalReviews;
  final Map<String, int> ratingDistribution;
  final DateTime lastUpdated;

  const RatingStatsModel({
    required this.vehicleId,
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    required this.lastUpdated,
  });

  factory RatingStatsModel.fromJson(Map<String, dynamic> json) => 
      _$RatingStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$RatingStatsModelToJson(this);

  /// Creates an empty rating stats model with default values
  factory RatingStatsModel.empty(String vehicleId) {
    return RatingStatsModel(
      vehicleId: vehicleId,
      averageRating: 0.0,
      totalReviews: 0,
      ratingDistribution: {
        '1': 0,
        '2': 0,
        '3': 0,
        '4': 0,
        '5': 0,
      },
      lastUpdated: DateTime.now(),
    );
  }

  /// Updates the rating statistics with a new rating
  RatingStatsModel addRating(double rating) {
    final ratingKey = rating.round().toString();
    final newDistribution = Map<String, int>.from(ratingDistribution);
    
    // Increment the count for this rating
    newDistribution[ratingKey] = (newDistribution[ratingKey] ?? 0) + 1;
    
    // Calculate new average
    final newTotalReviews = totalReviews + 1;
    final newTotalScore = (averageRating * totalReviews) + rating;
    final newAverageRating = newTotalScore / newTotalReviews;
    
    return RatingStatsModel(
      vehicleId: vehicleId,
      averageRating: newAverageRating,
      totalReviews: newTotalReviews,
      ratingDistribution: newDistribution,
      lastUpdated: DateTime.now(),
    );
  }

  /// Updates the rating statistics when a rating is updated
  RatingStatsModel updateRating(double oldRating, double newRating) {
    final oldRatingKey = oldRating.round().toString();
    final newRatingKey = newRating.round().toString();
    final newDistribution = Map<String, int>.from(ratingDistribution);
    
    // Decrement the count for the old rating
    newDistribution[oldRatingKey] = (newDistribution[oldRatingKey] ?? 1) - 1;
    
    // Increment the count for the new rating
    newDistribution[newRatingKey] = (newDistribution[newRatingKey] ?? 0) + 1;
    
    // Calculate new average
    final newTotalScore = (averageRating * totalReviews) - oldRating + newRating;
    final newAverageRating = newTotalScore / totalReviews;
    
    return RatingStatsModel(
      vehicleId: vehicleId,
      averageRating: newAverageRating,
      totalReviews: totalReviews,
      ratingDistribution: newDistribution,
      lastUpdated: DateTime.now(),
    );
  }

  /// Updates the rating statistics when a rating is removed
  RatingStatsModel removeRating(double rating) {
    if (totalReviews <= 1) {
      return RatingStatsModel.empty(vehicleId);
    }
    
    final ratingKey = rating.round().toString();
    final newDistribution = Map<String, int>.from(ratingDistribution);
    
    // Decrement the count for this rating
    newDistribution[ratingKey] = (newDistribution[ratingKey] ?? 1) - 1;
    
    // Calculate new average
    final newTotalReviews = totalReviews - 1;
    final newTotalScore = (averageRating * totalReviews) - rating;
    final newAverageRating = newTotalScore / newTotalReviews;
    
    return RatingStatsModel(
      vehicleId: vehicleId,
      averageRating: newAverageRating,
      totalReviews: newTotalReviews,
      ratingDistribution: newDistribution,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
    vehicleId,
    averageRating,
    totalReviews,
    ratingDistribution,
    lastUpdated,
  ];
}
