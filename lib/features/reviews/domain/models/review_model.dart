import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'review_model.g.dart';

@JsonSerializable()
class ReviewModel extends Equatable {
  final String id;
  final String vehicleId;
  final String userId;
  final String? bookingId;
  final double rating;
  final String comment;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const ReviewModel({
    required this.id,
    required this.vehicleId,
    required this.userId,
    this.bookingId,
    required this.rating,
    required this.comment,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) => 
      _$ReviewModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReviewModelToJson(this);

  ReviewModel copyWith({
    String? id,
    String? vehicleId,
    String? userId,
    String? bookingId,
    double? rating,
    String? comment,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      userId: userId ?? this.userId,
      bookingId: bookingId ?? this.bookingId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
    id,
    vehicleId,
    userId,
    bookingId,
    rating,
    comment,
    createdAt,
    updatedAt,
    metadata,
  ];
}
