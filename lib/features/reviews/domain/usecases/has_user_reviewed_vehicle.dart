import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class HasUserReviewedVehicle implements UseCase<bool, HasUserReviewedVehicleParams> {
  final ReviewRepository repository;

  HasUserReviewedVehicle(this.repository);

  @override
  Future<Either<Failure, bool>> call(HasUserReviewedVehicleParams params) {
    return repository.hasUserReviewedVehicle(
      userId: params.userId,
      vehicleId: params.vehicleId,
    );
  }
}

class HasUserReviewedVehicleParams extends Equatable {
  final String userId;
  final String vehicleId;

  const HasUserReviewedVehicleParams({
    required this.userId,
    required this.vehicleId,
  });

  @override
  List<Object> get props => [userId, vehicleId];
}
