import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/rating_stats_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class GetVehicleRatingStats implements UseCase<RatingStatsModel, GetVehicleRatingStatsParams> {
  final ReviewRepository repository;

  GetVehicleRatingStats(this.repository);

  @override
  Future<Either<Failure, RatingStatsModel>> call(GetVehicleRatingStatsParams params) {
    return repository.getVehicleRatingStats(params.vehicleId);
  }
}

class GetVehicleRatingStatsParams extends Equatable {
  final String vehicleId;

  const GetVehicleRatingStatsParams({required this.vehicleId});

  @override
  List<Object> get props => [vehicleId];
}
