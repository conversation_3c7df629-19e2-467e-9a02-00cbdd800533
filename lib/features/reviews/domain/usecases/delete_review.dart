import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class DeleteReview implements UseCase<bool, DeleteReviewParams> {
  final ReviewRepository repository;

  DeleteReview(this.repository);

  @override
  Future<Either<Failure, bool>> call(DeleteReviewParams params) {
    return repository.deleteReview(params.reviewId);
  }
}

class DeleteReviewParams extends Equatable {
  final String reviewId;

  const DeleteReviewParams({required this.reviewId});

  @override
  List<Object> get props => [reviewId];
}
