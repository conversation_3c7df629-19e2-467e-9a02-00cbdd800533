import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class AddReview implements UseCase<ReviewModel, AddReviewParams> {
  final ReviewRepository repository;

  AddReview(this.repository);

  @override
  Future<Either<Failure, ReviewModel>> call(AddReviewParams params) {
    return repository.addReview(
      vehicleId: params.vehicleId,
      userId: params.userId,
      bookingId: params.bookingId,
      rating: params.rating,
      comment: params.comment,
    );
  }
}

class AddReviewParams extends Equatable {
  final String vehicleId;
  final String userId;
  final String? bookingId;
  final double rating;
  final String comment;

  const AddReviewParams({
    required this.vehicleId,
    required this.userId,
    this.bookingId,
    required this.rating,
    required this.comment,
  });

  @override
  List<Object?> get props => [vehicleId, userId, bookingId, rating, comment];
}
