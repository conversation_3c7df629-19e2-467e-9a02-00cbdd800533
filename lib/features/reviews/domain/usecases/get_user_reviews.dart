import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class GetUserReviews implements UseCase<List<ReviewModel>, GetUserReviewsParams> {
  final ReviewRepository repository;

  GetUserReviews(this.repository);

  @override
  Future<Either<Failure, List<ReviewModel>>> call(GetUserReviewsParams params) {
    return repository.getUserReviews(params.userId);
  }
}

class GetUserReviewsParams extends Equatable {
  final String userId;

  const GetUserReviewsParams({required this.userId});

  @override
  List<Object> get props => [userId];
}
