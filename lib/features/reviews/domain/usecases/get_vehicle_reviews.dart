import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class GetVehicleReviews implements UseCase<List<ReviewModel>, GetVehicleReviewsParams> {
  final ReviewRepository repository;

  GetVehicleReviews(this.repository);

  @override
  Future<Either<Failure, List<ReviewModel>>> call(GetVehicleReviewsParams params) {
    return repository.getVehicleReviews(params.vehicleId);
  }
}

class GetVehicleReviewsParams extends Equatable {
  final String vehicleId;

  const GetVehicleReviewsParams({required this.vehicleId});

  @override
  List<Object> get props => [vehicleId];
}
