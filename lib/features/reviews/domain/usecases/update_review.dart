import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/repositories/review_repository.dart';

class UpdateReview implements UseCase<ReviewModel, UpdateReviewParams> {
  final ReviewRepository repository;

  UpdateReview(this.repository);

  @override
  Future<Either<Failure, ReviewModel>> call(UpdateReviewParams params) {
    return repository.updateReview(
      reviewId: params.reviewId,
      rating: params.rating,
      comment: params.comment,
    );
  }
}

class UpdateReviewParams extends Equatable {
  final String reviewId;
  final double? rating;
  final String? comment;

  const UpdateReviewParams({
    required this.reviewId,
    this.rating,
    this.comment,
  });

  @override
  List<Object?> get props => [reviewId, rating, comment];
}
