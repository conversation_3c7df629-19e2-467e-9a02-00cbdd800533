import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/rating_stats_model.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';

abstract class ReviewRepository {
  /// Get reviews for a specific vehicle
  Future<Either<Failure, List<ReviewModel>>> getVehicleReviews(String vehicleId);
  
  /// Get reviews by a specific user
  Future<Either<Failure, List<ReviewModel>>> getUserReviews(String userId);
  
  /// Get a specific review by ID
  Future<Either<Failure, ReviewModel>> getReview(String reviewId);
  
  /// Add a new review
  Future<Either<Failure, ReviewModel>> addReview({
    required String vehicleId,
    required String userId,
    String? bookingId,
    required double rating,
    required String comment,
  });
  
  /// Update an existing review
  Future<Either<Failure, ReviewModel>> updateReview({
    required String reviewId,
    double? rating,
    String? comment,
  });
  
  /// Delete a review
  Future<Either<Failure, bool>> deleteReview(String reviewId);
  
  /// Get rating statistics for a vehicle
  Future<Either<Failure, RatingStatsModel>> getVehicleRatingStats(String vehicleId);
  
  /// Check if user has already reviewed a vehicle
  Future<Either<Failure, bool>> hasUserReviewedVehicle({
    required String userId,
    required String vehicleId,
  });
}
