import 'package:equatable/equatable.dart';

class ConversationModel extends Equatable {
  final String id;
  final List<String> participantIds;
  final String? lastMessageContent;
  final DateTime? lastMessageTimestamp;
  final bool isUnread;
  final Map<String, dynamic>? metadata;

  const ConversationModel({
    required this.id,
    required this.participantIds,
    this.lastMessageContent,
    this.lastMessageTimestamp,
    this.isUnread = false,
    this.metadata,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) => ConversationModel(
    id: json['id'] as String,
    participantIds: (json['participants'] as List<dynamic>)
        .map((participant) => participant['user_id'] as String)
        .toList(),
    lastMessageContent: json['last_message_content'] as String?,
    lastMessageTimestamp: json['last_message_timestamp'] != null
        ? DateTime.parse(json['last_message_timestamp'] as String)
        : null,
    isUnread: json['is_unread'] as bool? ?? false,
    metadata: json['metadata'] as Map<String, dynamic>?,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'last_message_content': lastMessageContent,
    'last_message_timestamp': lastMessageTimestamp?.toIso8601String(),
    'is_unread': isUnread,
    'metadata': metadata,
  };

  ConversationModel copyWith({
    String? id,
    List<String>? participantIds,
    String? lastMessageContent,
    DateTime? lastMessageTimestamp,
    bool? isUnread,
    Map<String, dynamic>? metadata,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      participantIds: participantIds ?? this.participantIds,
      lastMessageContent: lastMessageContent ?? this.lastMessageContent,
      lastMessageTimestamp: lastMessageTimestamp ?? this.lastMessageTimestamp,
      isUnread: isUnread ?? this.isUnread,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
    id,
    participantIds,
    lastMessageContent,
    lastMessageTimestamp,
    isUnread,
    metadata,
  ];
}
