import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/conversation_model.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/message_model.dart';

abstract class MessagingRepository {
  /// Get all conversations for a user
  Future<Either<Failure, List<ConversationModel>>> getUserConversations(String userId);
  
  /// Get messages for a specific conversation
  Future<Either<Failure, List<MessageModel>>> getConversationMessages(String conversationId);
  
  /// Send a message
  Future<Either<Failure, MessageModel>> sendMessage({
    required String conversationId,
    required String senderId,
    required String receiverId,
    required String content,
  });
  
  /// Create a new conversation
  Future<Either<Failure, ConversationModel>> createConversation({
    required List<String> participantIds,
    Map<String, dynamic>? metadata,
  });
  
  /// Mark messages as read
  Future<Either<Failure, bool>> markMessagesAsRead({
    required String conversationId,
    required String userId,
  });
  
  /// Delete a message
  Future<Either<Failure, bool>> deleteMessage(String messageId);
  
  /// Delete a conversation
  Future<Either<Failure, bool>> deleteConversation(String conversationId);
  
  /// Get unread message count for a user
  Future<Either<Failure, int>> getUnreadMessageCount(String userId);
  
  /// Stream of messages for a specific conversation
  Stream<Either<Failure, List<MessageModel>>> messagesStream(String conversationId);
  
  /// Stream of conversations for a user
  Stream<Either<Failure, List<ConversationModel>>> conversationsStream(String userId);
}
