import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/conversation_model.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/message_model.dart';
import 'package:gaadi_sewa/features/messaging/domain/repositories/messaging_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class MessagingRepositoryImpl implements MessagingRepository {
  final NetworkInfo networkInfo;
  final SupabaseClient supabaseClient;
  final _uuid = const Uuid();

  MessagingRepositoryImpl({
    required this.networkInfo,
    required this.supabaseClient,
  });

  @override
  Future<Either<Failure, List<ConversationModel>>> getUserConversations(String userId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('conversations')
          .select('*, participants:conversation_participants(*)')
          .contains('participants.user_id', [userId])
          .order('last_message_timestamp', ascending: false);

      final conversations = (response as List)
          .map((json) => ConversationModel.fromJson(json as Map<String, dynamic>))
          .toList();

      return Right(conversations);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MessageModel>>> getConversationMessages(String conversationId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .order('timestamp', ascending: true);

      final messages = (response as List)
          .map((json) => MessageModel.fromJson(json as Map<String, dynamic>))
          .toList();

      return Right(messages);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, MessageModel>> sendMessage({
    required String conversationId,
    required String senderId,
    required String receiverId,
    required String content,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final messageId = _uuid.v4();
      final timestamp = DateTime.now();

      // Create message data
      final messageData = {
        'id': messageId,
        'conversation_id': conversationId,
        'sender_id': senderId,
        'receiver_id': receiverId,
        'content': content,
        'timestamp': timestamp.toIso8601String(),
        'status': MessageStatus.sent.toString().split('.').last,
      };

      // Insert the message
      final response = await supabaseClient
          .from('messages')
          .insert(messageData)
          .select()
          .single();

      // Update the conversation with last message info
      await supabaseClient
          .from('conversations')
          .update({
            'last_message_content': content,
            'last_message_timestamp': timestamp.toIso8601String(),
            'is_unread': true,
          })
          .eq('id', conversationId);

      final message = MessageModel.fromJson(response);
      return Right(message);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, ConversationModel>> createConversation({
    required List<String> participantIds,
    Map<String, dynamic>? metadata,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Check if conversation already exists between these participants
      final existingConversation = await _findExistingConversation(participantIds);
      if (existingConversation != null) {
        return Right(existingConversation);
      }

      // Create new conversation
      final conversationId = _uuid.v4();
      final timestamp = DateTime.now();

      // Create conversation data
      final conversationData = {
        'id': conversationId,
        'last_message_timestamp': timestamp.toIso8601String(),
        'is_unread': false,
        'metadata': metadata,
      };

      // Insert the conversation
      await supabaseClient
          .from('conversations')
          .insert(conversationData);

      // Add participants to the conversation
      final participantData = participantIds.map((userId) => {
        'conversation_id': conversationId,
        'user_id': userId,
      }).toList();

      await supabaseClient
          .from('conversation_participants')
          .insert(participantData);

      // Get the created conversation
      final response = await supabaseClient
          .from('conversations')
          .select()
          .eq('id', conversationId)
          .single();

      final conversation = ConversationModel.fromJson(response);
      return Right(conversation);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> markMessagesAsRead({
    required String conversationId,
    required String userId,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Update message status
      await supabaseClient
          .from('messages')
          .update({
            'status': MessageStatus.read.toString().split('.').last,
          })
          .eq('conversation_id', conversationId)
          .eq('receiver_id', userId)
          .neq('status', MessageStatus.read.toString().split('.').last);

      // Update conversation unread status
      await supabaseClient
          .from('conversations')
          .update({
            'is_unread': false,
          })
          .eq('id', conversationId);

      return const Right(true);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteMessage(String messageId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      await supabaseClient
          .from('messages')
          .delete()
          .eq('id', messageId);

      return const Right(true);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteConversation(String conversationId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Delete all messages in the conversation
      await supabaseClient
          .from('messages')
          .delete()
          .eq('conversation_id', conversationId);

      // Delete all participants
      await supabaseClient
          .from('conversation_participants')
          .delete()
          .eq('conversation_id', conversationId);

      // Delete the conversation
      await supabaseClient
          .from('conversations')
          .delete()
          .eq('id', conversationId);

      return const Right(true);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getUnreadMessageCount(String userId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('messages')
          .select('id')
          .eq('receiver_id', userId)
          .neq('status', MessageStatus.read.toString().split('.').last);

      return Right((response as List).length);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<Either<Failure, List<MessageModel>>> messagesStream(String conversationId) {
    return supabaseClient
        .from('messages')
        .stream(primaryKey: ['id'])
        .eq('conversation_id', conversationId)
        .order('timestamp')
        .map((data) {
          try {
            final messages = data
                .map((json) => MessageModel.fromJson(json))
                .toList();
            return Right<Failure, List<MessageModel>>(messages);
          } catch (e) {
            return Left<Failure, List<MessageModel>>(ServerFailure(message: 'Error processing messages: $e'));
          }
        })
        .handleError((e) {
          return Stream.value(Left<Failure, List<MessageModel>>(ServerFailure(message: 'Stream error: $e')));
        });
  }

  @override
  Stream<Either<Failure, List<ConversationModel>>> conversationsStream(String userId) {
    return supabaseClient
        .from('conversations')
        .stream(primaryKey: ['id'])
        .map((data) {
          try {
            final conversations = data
                .map((json) => ConversationModel.fromJson(json))
                .toList();
            return Right<Failure, List<ConversationModel>>(conversations);
          } catch (e) {
            return Left<Failure, List<ConversationModel>>(ServerFailure(message: 'Error processing conversations: $e'));
          }
        })
        .handleError((e) {
          return Stream.value(Left<Failure, List<ConversationModel>>(ServerFailure(message: 'Stream error: $e')));
        });
  }

  // Helper method to find existing conversation between participants
  Future<ConversationModel?> _findExistingConversation(List<String> participantIds) async {
    try {
      if (participantIds.isEmpty) {
        return null;
      }
      
      // Get all conversations for the first participant
      final response = await supabaseClient
          .from('conversation_participants')
          .select('conversation_id')
          .eq('user_id', participantIds.first);
      
      if (response.isEmpty) {
        return null;
      }
      
      // Extract conversation IDs
      final conversationIds = response
          .map((item) => item['conversation_id'] as String)
          .toList();

      // For each potential conversation, check if all participants are included
      for (final conversationId in conversationIds) {
        // Check if all participants are in this conversation
        bool allParticipantsIncluded = true;
        
        for (final userId in participantIds) {
          final participantCheck = await supabaseClient
              .from('conversation_participants')
              .select()
              .eq('conversation_id', conversationId)
              .eq('user_id', userId);
          
          if (participantCheck.isEmpty) {
            allParticipantsIncluded = false;
            break;
          }
        }
        
        // If all participants are in this conversation, return it
        if (allParticipantsIncluded) {
          final conversationResponse = await supabaseClient
              .from('conversations')
              .select()
              .eq('id', conversationId)
              .single();
          
          return ConversationModel.fromJson(conversationResponse);
        }
      }
      
      // No matching conversation found
      return null;

    } catch (e) {
      return null;
    }
  }
}
