import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/presentation/widgets/app_error_widget.dart';
import 'package:gaadi_sewa/core/presentation/widgets/app_loading_indicator.dart';
import 'package:gaadi_sewa/features/messaging/presentation/providers/messaging_providers.dart';
import 'package:gaadi_sewa/features/messaging/presentation/widgets/message_bubble.dart';
import 'package:gaadi_sewa/features/messaging/presentation/widgets/message_input.dart';
import 'package:gaadi_sewa/features/user/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/user/presentation/providers/user_provider.dart';

class ConversationScreen extends ConsumerStatefulWidget {
  static const String routeName = '/conversation';
  final String conversationId;

  const ConversationScreen({
    Key? key,
    required this.conversationId,
  }) : super(key: key);

  @override
  ConsumerState<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends ConsumerState<ConversationScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Mark messages as read when opening the conversation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser != null) {
        ref.read(messagingRepositoryProvider).markMessagesAsRead(
              conversationId: widget.conversationId,
              userId: currentUser.id,
            );
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final messagesAsync =
        ref.watch(conversationMessagesProvider(widget.conversationId));
    final conversationAsync =
        ref.watch(conversationProvider(widget.conversationId));

    // Scroll to bottom when new messages arrive
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (messagesAsync.hasValue) {
        _scrollToBottom();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: conversationAsync.when(
          data: (conversation) {
            // Find the other participant (not the current user)
            final otherParticipantId = conversation.participantIds
                .firstWhere((id) => id != currentUser?.id, orElse: () => '');

            // Get the other participant's profile
            final otherParticipantAsync =
                ref.watch(userProfileProvider(otherParticipantId));

            return otherParticipantAsync.when(
              data: (profile) => Text(profile?.fullName ?? 'Chat'),
              loading: () => const Text('Loading...'),
              error: (_, __) => const Text('Chat'),
            );
          },
          loading: () => const Text('Loading...'),
          error: (_, __) => const Text('Chat'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // TODO: Show conversation info
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: messagesAsync.when(
              data: (messages) {
                if (messages.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.message_outlined,
                          size: 80,
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No messages yet',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Start the conversation by sending a message',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isCurrentUser = message.senderId == currentUser?.id;

                    // Show date header if needed
                    final showDateHeader = index == 0 ||
                        !_isSameDay(
                            messages[index - 1].timestamp, message.timestamp);

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        if (showDateHeader) _buildDateHeader(message.timestamp),
                        MessageBubble(
                          message: message,
                          isCurrentUser: isCurrentUser,
                        ),
                      ],
                    );
                  },
                );
              },
              loading: () => const AppLoadingIndicator(),
              error: (error, stackTrace) => AppErrorWidget(
                message: 'Failed to load messages: $error',
                onRetry: () => ref.refresh(
                    conversationMessagesProvider(widget.conversationId)),
              ),
            ),
          ),

          // Message input
          MessageInput(
            controller: _messageController,
            onSend: _sendMessage,
          ),
        ],
      ),
    );
  }

  Widget _buildDateHeader(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate =
        DateTime(timestamp.year, timestamp.month, timestamp.day);

    String dateText;
    if (messageDate == today) {
      dateText = 'Today';
    } else if (messageDate == yesterday) {
      dateText = 'Yesterday';
    } else {
      dateText = '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            dateText,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ),
      ),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  void _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    final currentUser = ref.read(currentUserProvider);
    if (currentUser == null) return;

    final conversation =
        await ref.read(conversationProvider(widget.conversationId).future);

    // Find the other participant (not the current user)
    final receiverId = conversation.participantIds
        .firstWhere((id) => id != currentUser.id, orElse: () => '');

    if (receiverId.isEmpty) return;

    // Clear the input field
    _messageController.clear();

    // Send the message
    await ref.read(messagingRepositoryProvider).sendMessage(
          conversationId: widget.conversationId,
          senderId: currentUser.id,
          receiverId: receiverId,
          content: message,
        );
  }
}
