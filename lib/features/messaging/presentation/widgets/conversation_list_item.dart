import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/conversation_model.dart';
import 'package:gaadi_sewa/features/user/domain/models/user_profile_model.dart';
import 'package:gaadi_sewa/features/user/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/user/presentation/providers/user_provider.dart';
import 'package:timeago/timeago.dart' as timeago;

class ConversationListItem extends ConsumerWidget {
  final ConversationModel conversation;
  final VoidCallback onTap;

  const ConversationListItem({
    Key? key,
    required this.conversation,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    
    // Find the other participant (not the current user)
    final otherParticipantId = conversation.participantIds
        .firstWhere((id) => id != currentUser?.id, orElse: () => '');
    
    // Get the other participant's profile
    final otherParticipantAsync = ref.watch(userProfileProvider(otherParticipantId));

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Profile image
            otherParticipantAsync.when(
              data: (profile) => _buildAvatar(profile),
              loading: () => const CircleAvatar(
                radius: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              error: (_, __) => const CircleAvatar(
                radius: 24,
                child: Icon(Icons.person),
              ),
            ),
            const SizedBox(width: 12),
            
            // Conversation details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  otherParticipantAsync.when(
                    data: (profile) => Text(
                      profile?.fullName ?? 'Unknown User',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: conversation.isUnread ? FontWeight.bold : FontWeight.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    loading: () => const Text('Loading...'),
                    error: (_, __) => const Text('Unknown User'),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Last message
                  Text(
                    conversation.lastMessageContent ?? 'No messages yet',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: conversation.isUnread 
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey[600],
                      fontWeight: conversation.isUnread ? FontWeight.bold : FontWeight.normal,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // Timestamp and unread indicator
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Timestamp
                if (conversation.lastMessageTimestamp != null)
                  Text(
                    timeago.format(conversation.lastMessageTimestamp!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                
                const SizedBox(height: 4),
                
                // Unread indicator
                if (conversation.isUnread)
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar(UserProfileModel? profile) {
    if (profile?.profileImageUrl != null && profile!.profileImageUrl!.isNotEmpty) {
      return CircleAvatar(
        radius: 24,
        backgroundImage: NetworkImage(profile.profileImageUrl!),
      );
    } else {
      return CircleAvatar(
        radius: 24,
        backgroundColor: Colors.grey[300],
        child: Text(
          profile?.fullName?.isNotEmpty == true 
              ? profile!.fullName!.substring(0, 1).toUpperCase() 
              : '?',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }
}
