import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/messaging/data/repositories/messaging_repository_impl.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/conversation_model.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/message_model.dart';
import 'package:gaadi_sewa/features/messaging/domain/repositories/messaging_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Repository providers
final messagingRepositoryProvider = Provider<MessagingRepository>((ref) {
  final supabaseClient = Supabase.instance.client;
  final networkInfo = ref.watch(networkInfoProvider);
  
  return MessagingRepositoryImpl(
    networkInfo: networkInfo,
    supabaseClient: supabaseClient,
  );
});

// Conversation providers
final userConversationsProvider = StreamProvider.family<List<ConversationModel>, String>(
  (ref, userId) {
    if (userId.isEmpty) {
      return Stream.value([]);
    }
    
    final repository = ref.watch(messagingRepositoryProvider);
    return repository.conversationsStream(userId).map((result) {
      return result.fold(
        (failure) => [],
        (conversations) => conversations,
      );
    });
  },
);

final conversationProvider = FutureProvider.family<ConversationModel, String>(
  (ref, conversationId) async {
    final repository = ref.watch(messagingRepositoryProvider);
    final conversationsResult = await repository.getUserConversations('');
    
    return conversationsResult.fold(
      (failure) => throw Exception(failure.message),
      (conversations) {
        final conversation = conversations.firstWhere(
          (c) => c.id == conversationId,
          orElse: () => throw Exception('Conversation not found'),
        );
        return conversation;
      },
    );
  },
);

// Message providers
final conversationMessagesProvider = StreamProvider.family<List<MessageModel>, String>(
  (ref, conversationId) {
    if (conversationId.isEmpty) {
      return Stream.value([]);
    }
    
    final repository = ref.watch(messagingRepositoryProvider);
    return repository.messagesStream(conversationId).map((result) {
      return result.fold(
        (failure) => [],
        (messages) => messages,
      );
    });
  },
);

final unreadMessageCountProvider = FutureProvider.family<int, String>(
  (ref, userId) async {
    if (userId.isEmpty) {
      return 0;
    }
    
    final repository = ref.watch(messagingRepositoryProvider);
    final result = await repository.getUnreadMessageCount(userId);
    
    return result.fold(
      (failure) => 0,
      (count) => count,
    );
  },
);

// Network info provider
final connectivityProvider = Provider<Connectivity>((ref) {
  return Connectivity();
});

final networkInfoProvider = Provider<NetworkInfo>((ref) {
  final connectivity = ref.watch(connectivityProvider);
  return NetworkInfoImpl(connectivity: connectivity);
});
