import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/widgets/app_bar_widget.dart';
import 'package:gaadi_sewa/core/widgets/loading_widget.dart';
import 'package:gaadi_sewa/features/profile/presentation/providers/profile_provider.dart';

class PreferencesScreen extends ConsumerStatefulWidget {
  static const routeName = '/preferences';

  const PreferencesScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<PreferencesScreen> createState() => _PreferencesScreenState();
}

class _PreferencesScreenState extends ConsumerState<PreferencesScreen> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  String _defaultCurrency = 'NPR';
  String _language = 'English';

  final List<String> _currencies = ['NPR', 'USD', 'EUR', 'INR'];
  final List<String> _languages = ['English', 'Nepali', 'Hindi'];

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    final profileState = ref.read(profileProvider);
    if (profileState.profile != null && profileState.profile!.preferences != null) {
      final prefs = profileState.profile!.preferences!;
      setState(() {
        _notificationsEnabled = prefs['notificationsEnabled'] ?? true;
        _darkModeEnabled = prefs['darkModeEnabled'] ?? false;
        _defaultCurrency = prefs['defaultCurrency'] ?? 'NPR';
        _language = prefs['language'] ?? 'English';
      });
    }
  }

  Future<void> _savePreferences() async {
    final preferences = {
      'notificationsEnabled': _notificationsEnabled,
      'darkModeEnabled': _darkModeEnabled,
      'defaultCurrency': _defaultCurrency,
      'language': _language,
    };

    await ref.read(profileProvider.notifier).updateProfile(
          preferences: preferences,
        );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Preferences saved')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileProvider);
    final isLoading = profileState.isLoading;

    return Scaffold(
      appBar: const AppBarWidget(title: 'Preferences'),
      body: isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'App Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildSwitchTile(
                    title: 'Notifications',
                    subtitle: 'Enable push notifications',
                    value: _notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        _notificationsEnabled = value;
                      });
                    },
                  ),
                  _buildSwitchTile(
                    title: 'Dark Mode',
                    subtitle: 'Enable dark theme',
                    value: _darkModeEnabled,
                    onChanged: (value) {
                      setState(() {
                        _darkModeEnabled = value;
                      });
                    },
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Regional Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDropdownTile(
                    title: 'Default Currency',
                    value: _defaultCurrency,
                    items: _currencies,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _defaultCurrency = value;
                        });
                      }
                    },
                  ),
                  _buildDropdownTile(
                    title: 'Language',
                    value: _language,
                    items: _languages,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _language = value;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _savePreferences,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Save Preferences'),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildDropdownTile<T>({
    required String title,
    required T value,
    required List<T> items,
    required ValueChanged<T?> onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<T>(
              value: value,
              items: items.map((item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: Text(item.toString()),
                );
              }).toList(),
              onChanged: onChanged,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
