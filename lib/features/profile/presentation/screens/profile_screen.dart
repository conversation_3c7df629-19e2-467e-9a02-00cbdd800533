import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/widgets/app_bar_widget.dart';
import 'package:gaadi_sewa/core/widgets/error_widget.dart';
import 'package:gaadi_sewa/core/widgets/loading_widget.dart';
import 'package:gaadi_sewa/features/profile/presentation/providers/profile_provider.dart';
import 'package:gaadi_sewa/features/profile/presentation/widgets/profile_avatar.dart';
import 'package:gaadi_sewa/features/profile/presentation/widgets/profile_info_item.dart';
import 'package:image_picker/image_picker.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  static const routeName = '/profile';

  const ProfileScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch user profile when screen loads
    Future.microtask(() => ref.read(profileProvider.notifier).getUserProfile());
  }

  Future<void> _pickAndUploadImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 800,
      imageQuality: 85,
    );

    if (image != null) {
      await ref.read(profileProvider.notifier).uploadProfileImage(image.path);
    }
  }

  void _showEditProfileDialog() {
    final profile = ref.read(profileProvider).profile;
    if (profile == null) return;

    final nameController = TextEditingController(text: profile.fullName);
    final phoneController = TextEditingController(text: profile.phone);
    final addressController = TextEditingController(text: profile.address);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: addressController,
                decoration: const InputDecoration(
                  labelText: 'Address',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(profileProvider.notifier).updateProfile(
                    fullName: nameController.text,
                    phone: phoneController.text,
                    address: addressController.text,
                  );
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileProvider);
    final profile = profileState.profile;
    final isLoading = profileState.isLoading;
    final failure = profileState.failure;

    return Scaffold(
      appBar: const AppBarWidget(title: 'My Profile'),
      body: isLoading
          ? const LoadingWidget()
          : failure != null
              ? AppErrorWidget(
                  failure: failure,
                  onRetry: () =>
                      ref.read(profileProvider.notifier).getUserProfile(),
                )
              : profile == null
                  ? const Center(
                      child: Text('No profile data available'),
                    )
                  : RefreshIndicator(
                      onRefresh: () async {
                        await ref
                            .read(profileProvider.notifier)
                            .getUserProfile();
                      },
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ProfileAvatar(
                              avatarUrl: profile.avatarUrl,
                              onTap: _pickAndUploadImage,
                            ),
                            const SizedBox(height: 24),
                            Text(
                              profile.fullName ?? 'No Name',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              profile.email ?? 'No Email',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 32),
                            const Divider(),
                            const SizedBox(height: 16),
                            ProfileInfoItem(
                              icon: Icons.phone,
                              title: 'Phone',
                              value: profile.phone ?? 'Not provided',
                            ),
                            const SizedBox(height: 16),
                            ProfileInfoItem(
                              icon: Icons.location_on,
                              title: 'Address',
                              value: profile.address ?? 'Not provided',
                            ),
                            const SizedBox(height: 16),
                            ProfileInfoItem(
                              icon: Icons.calendar_today,
                              title: 'Member Since',
                              value: _formatDate(profile.createdAt),
                            ),
                            const SizedBox(height: 32),
                            ElevatedButton.icon(
                              onPressed: _showEditProfileDialog,
                              icon: const Icon(Icons.edit),
                              label: const Text('Edit Profile'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            OutlinedButton.icon(
                              onPressed: () {
                                // TODO: Implement preferences screen navigation
                              },
                              icon: const Icon(Icons.settings),
                              label: const Text('Preferences'),
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
