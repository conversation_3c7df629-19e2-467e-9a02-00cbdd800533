import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/profile/data/repositories/profile_repository_impl.dart';
import 'package:gaadi_sewa/features/profile/domain/models/profile_model.dart';
import 'package:gaadi_sewa/features/profile/domain/repositories/profile_repository.dart';
import 'package:gaadi_sewa/features/profile/domain/usecases/get_user_profile.dart';
import 'package:gaadi_sewa/features/profile/domain/usecases/update_profile.dart';
import 'package:gaadi_sewa/features/profile/domain/usecases/upload_profile_image.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

// Repository provider
final profileRepositoryProvider = Provider<ProfileRepository>((ref) {
  return ProfileRepositoryImpl(
    networkInfo: ref.read(networkInfoProvider),
    client: http.Client(),
    supabaseClient: Supabase.instance.client,
  );
});

// Use case providers
final getUserProfileProvider = Provider<GetUserProfile>((ref) {
  return GetUserProfile(ref.read(profileRepositoryProvider));
});

final updateProfileProvider = Provider<UpdateProfile>((ref) {
  return UpdateProfile(ref.read(profileRepositoryProvider));
});

final uploadProfileImageProvider = Provider<UploadProfileImage>((ref) {
  return UploadProfileImage(ref.read(profileRepositoryProvider));
});

// State notifier for profile
class ProfileState {
  final ProfileModel? profile;
  final bool isLoading;
  final Failure? failure;

  ProfileState({
    this.profile,
    this.isLoading = false,
    this.failure,
  });

  ProfileState copyWith({
    ProfileModel? profile,
    bool? isLoading,
    Failure? failure,
  }) {
    return ProfileState(
      profile: profile ?? this.profile,
      isLoading: isLoading ?? this.isLoading,
      failure: failure,
    );
  }
}

class ProfileNotifier extends StateNotifier<ProfileState> {
  final GetUserProfile _getUserProfile;
  final UpdateProfile _updateProfile;
  final UploadProfileImage _uploadProfileImage;

  ProfileNotifier({
    required GetUserProfile getUserProfile,
    required UpdateProfile updateProfile,
    required UploadProfileImage uploadProfileImage,
  })  : _getUserProfile = getUserProfile,
        _updateProfile = updateProfile,
        _uploadProfileImage = uploadProfileImage,
        super(ProfileState());

  Future<void> getUserProfile() async {
    state = state.copyWith(isLoading: true, failure: null);
    final result = await _getUserProfile(NoParams());
    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (profile) => state = state.copyWith(isLoading: false, profile: profile),
    );
  }

  Future<void> updateProfile({
    String? fullName,
    String? phone,
    String? address,
    Map<String, dynamic>? preferences,
  }) async {
    state = state.copyWith(isLoading: true, failure: null);
    final params = UpdateProfileParams(
      fullName: fullName,
      phone: phone,
      address: address,
      preferences: preferences,
    );
    final result = await _updateProfile(params);
    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (profile) => state = state.copyWith(isLoading: false, profile: profile),
    );
  }

  Future<String?> uploadProfileImage(String imagePath) async {
    state = state.copyWith(isLoading: true, failure: null);
    final params = UploadProfileImageParams(imagePath: imagePath);
    final result = await _uploadProfileImage(params);
    return result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, failure: failure);
        return null;
      },
      (imageUrl) {
        // Update the profile with the new avatar URL
        if (state.profile != null) {
          state = state.copyWith(
            isLoading: false,
            profile: state.profile!.copyWith(avatarUrl: imageUrl),
          );
        } else {
          state = state.copyWith(isLoading: false);
        }
        return imageUrl;
      },
    );
  }
}

// Profile provider
final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>((ref) {
  return ProfileNotifier(
    getUserProfile: ref.read(getUserProfileProvider),
    updateProfile: ref.read(updateProfileProvider),
    uploadProfileImage: ref.read(uploadProfileImageProvider),
  );
});
