import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/profile/domain/models/profile_model.dart';
import 'package:gaadi_sewa/features/profile/domain/repositories/profile_repository.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final NetworkInfo networkInfo;
  final http.Client client;
  final SupabaseClient supabaseClient;

  ProfileRepositoryImpl({
    required this.networkInfo,
    required this.client,
    required this.supabaseClient,
  });

  @override
  Future<Either<Failure, ProfileModel>> getUserProfile() async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      final response = await supabaseClient
          .from('profiles')
          .select()
          .eq('id', user.id)
          .single();

      final profile = ProfileModel.fromJson(response);
      return Right(profile);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure());
      }
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, ProfileModel>> updateProfile({
    String? fullName,
    String? phone,
    String? address,
    Map<String, dynamic>? preferences,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      final updateData = {
        if (fullName != null) 'full_name': fullName,
        if (phone != null) 'phone': phone,
        if (address != null) 'address': address,
        if (preferences != null) 'preferences': preferences,
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('profiles')
          .update(updateData)
          .eq('id', user.id)
          .select()
          .single();

      final updatedProfile = ProfileModel.fromJson(response);
      return Right(updatedProfile);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> uploadProfileImage(String imagePath) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      final file = File(imagePath);
      if (!await file.exists()) {
        return Left(InvalidInputFailure(message: 'File does not exist'));
      }

      final fileExt = imagePath.split('.').last;
      final fileName = '${user.id}_${DateTime.now().millisecondsSinceEpoch}.$fileExt';
      final filePath = 'avatars/$fileName';

      await supabaseClient.storage.from('avatars').upload(
            filePath,
            file,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      final imageUrl = supabaseClient.storage.from('avatars').getPublicUrl(filePath);

      // Update the user's profile with the new avatar URL
      await supabaseClient
          .from('profiles')
          .update({'avatar_url': imageUrl, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', user.id);

      return Right(imageUrl);
    } on StorageException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUserPreferences() async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      final response = await supabaseClient
          .from('profiles')
          .select('preferences')
          .eq('id', user.id)
          .single();

      final preferences = response['preferences'] as Map<String, dynamic>? ?? {};
      return Right(preferences);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> updateUserPreferences(
    Map<String, dynamic> preferences,
  ) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure(message: 'No internet connection'));
    }

    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      // Get current preferences first
      final currentPrefsResponse = await supabaseClient
          .from('profiles')
          .select('preferences')
          .eq('id', user.id)
          .single();

      final currentPrefs = currentPrefsResponse['preferences'] as Map<String, dynamic>? ?? {};
      
      // Merge current preferences with new ones
      final updatedPrefs = {...currentPrefs, ...preferences};

      // Update preferences
      await supabaseClient
          .from('profiles')
          .update({
            'preferences': updatedPrefs,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id);

      return Right(updatedPrefs);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
