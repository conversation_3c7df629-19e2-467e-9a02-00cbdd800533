import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/profile/domain/models/profile_model.dart';

abstract class ProfileRepository {
  /// Get the current user's profile
  Future<Either<Failure, ProfileModel>> getUserProfile();
  
  /// Update the user's profile
  Future<Either<Failure, ProfileModel>> updateProfile({
    String? fullName,
    String? phone,
    String? address,
    Map<String, dynamic>? preferences,
  });
  
  /// Upload a profile image and update the avatar URL
  Future<Either<Failure, String>> uploadProfileImage(String imagePath);
  
  /// Get user preferences
  Future<Either<Failure, Map<String, dynamic>>> getUserPreferences();
  
  /// Update user preferences
  Future<Either<Failure, Map<String, dynamic>>> updateUserPreferences(
    Map<String, dynamic> preferences,
  );
}
