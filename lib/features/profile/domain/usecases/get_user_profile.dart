import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/profile/domain/models/profile_model.dart';
import 'package:gaadi_sewa/features/profile/domain/repositories/profile_repository.dart';

class GetUserProfile implements UseCase<ProfileModel, NoParams> {
  final ProfileRepository repository;

  GetUserProfile(this.repository);

  @override
  Future<Either<Failure, ProfileModel>> call(NoParams params) {
    return repository.getUserProfile();
  }
}
