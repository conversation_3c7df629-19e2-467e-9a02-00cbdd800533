import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/profile/domain/repositories/profile_repository.dart';

class UploadProfileImage implements UseCase<String, UploadProfileImageParams> {
  final ProfileRepository repository;

  UploadProfileImage(this.repository);

  @override
  Future<Either<Failure, String>> call(UploadProfileImageParams params) {
    return repository.uploadProfileImage(params.imagePath);
  }
}

class UploadProfileImageParams extends Equatable {
  final String imagePath;

  const UploadProfileImageParams({required this.imagePath});

  @override
  List<Object> get props => [imagePath];
}
