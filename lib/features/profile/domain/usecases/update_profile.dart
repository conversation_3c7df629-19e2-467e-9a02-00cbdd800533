import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/profile/domain/models/profile_model.dart';
import 'package:gaadi_sewa/features/profile/domain/repositories/profile_repository.dart';

class UpdateProfile implements UseCase<ProfileModel, UpdateProfileParams> {
  final ProfileRepository repository;

  UpdateProfile(this.repository);

  @override
  Future<Either<Failure, ProfileModel>> call(UpdateProfileParams params) {
    return repository.updateProfile(
      fullName: params.fullName,
      phone: params.phone,
      address: params.address,
      preferences: params.preferences,
    );
  }
}

class UpdateProfileParams extends Equatable {
  final String? fullName;
  final String? phone;
  final String? address;
  final Map<String, dynamic>? preferences;

  const UpdateProfileParams({
    this.fullName,
    this.phone,
    this.address,
    this.preferences,
  });

  @override
  List<Object?> get props => [fullName, phone, address, preferences];
}
