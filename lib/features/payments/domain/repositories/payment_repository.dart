import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';

abstract class PaymentRepository {
  // Process a new payment
  Future<Either<Failure, PaymentModel>> processPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
    Map<String, dynamic>? metadata,
  });

  // Get payment by ID
  Future<Either<Failure, PaymentModel>> getPayment(String paymentId);

  // Get payments by booking ID
  Future<Either<Failure, List<PaymentModel>>> getPaymentsByBooking(String bookingId);

  // Update payment status
  Future<Either<Failure, PaymentModel>> updatePaymentStatus({
    required String paymentId,
    required String status,
    String? transactionId,
    Map<String, dynamic>? metadata,
  });

  // Verify payment with payment gateway
  Future<Either<Failure, bool>> verifyPayment({
    required String paymentId,
    required String transactionId,
  });
}
