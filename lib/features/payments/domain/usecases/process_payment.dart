import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/usecases/usecase.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';
import 'package:gaadi_sewa/features/payments/domain/repositories/payment_repository.dart';

class ProcessPayment implements UseCase<PaymentModel, ProcessPaymentParams> {
  final PaymentRepository repository;

  ProcessPayment(this.repository);

  @override
  Future<Either<Failure, PaymentModel>> call(ProcessPaymentParams params) async {
    return await repository.processPayment(
      bookingId: params.bookingId,
      amount: params.amount,
      paymentMethod: params.paymentMethod,
      metadata: params.metadata,
    );
  }
}

class ProcessPaymentParams {
  final String bookingId;
  final double amount;
  final String paymentMethod;
  final Map<String, dynamic>? metadata;

  ProcessPaymentParams({
    required this.bookingId,
    required this.amount,
    required this.paymentMethod,
    this.metadata,
  });
}
