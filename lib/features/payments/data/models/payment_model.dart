import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'payment_model.g.dart';

@JsonEnum()
enum PaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('refunded')
  refunded,
  @JsonValue('cancelled')
  cancelled,
}

@JsonEnum()
enum PaymentMethod {
  @JsonValue('khalti')
  khalti,
  @JsonValue('esewa')
  esewa,
  @JsonValue('cash')
  cash,
  @JsonValue('bank_transfer')
  bankTransfer,
}

@JsonSerializable(explicitToJson: true)
class PaymentModel extends Equatable {
  @Json<PERSON>ey(name: 'id')
  final String id;
  
  @JsonKey(name: 'booking_id')
  final String bookingId;
  
  @JsonKey(name: 'amount')
  final double amount;
  
  @<PERSON>sonKey(name: 'currency', defaultValue: 'NPR')
  final String currency;
  
  @<PERSON>sonKey(name: 'status')
  final PaymentStatus status;
  
  @JsonKey(name: 'payment_method')
  final PaymentMethod paymentMethod;
  
  @JsonKey(name: 'transaction_id')
  final String? transactionId;
  
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  
  @JsonKey(name: 'metadata')
  final Map<String, dynamic>? metadata;

  PaymentModel({
    required this.id,
    required this.bookingId,
    required this.amount,
    this.currency = 'NPR',
    this.status = PaymentStatus.pending,
    required this.paymentMethod,
    this.transactionId,
    DateTime? createdAt,
    this.updatedAt,
    this.metadata,
  }) : createdAt = createdAt ?? DateTime.now();

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'] as String,
      bookingId: json['booking_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'NPR',
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['payment_method'],
        orElse: () => PaymentMethod.cash,
      ),
      transactionId: json['transaction_id'] as String?,
      createdAt: json['created_at'] == null 
          ? DateTime.now() 
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null 
          ? null 
          : DateTime.parse(json['updated_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'booking_id': bookingId,
      'amount': amount,
      'currency': currency,
      'status': status.toString().split('.').last,
      'payment_method': paymentMethod.toString().split('.').last,
      'transaction_id': transactionId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  PaymentModel copyWith({
    String? id,
    String? bookingId,
    double? amount,
    String? currency,
    PaymentStatus? status,
    PaymentMethod? paymentMethod,
    String? transactionId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionId: transactionId ?? this.transactionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        bookingId,
        amount,
        currency,
        status,
        paymentMethod,
        transactionId,
        createdAt,
        updatedAt,
        metadata,
      ];

  @override
  bool get stringify => true;
}
