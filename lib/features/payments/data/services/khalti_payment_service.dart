import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:gaadi_sewa/core/constants/api_constants.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';

class KhaltiPaymentService {
  final Dio _dio;
  final String _publicKey;
  final String _secretKey;

  KhaltiPaymentService({
    required Dio dio,
    required String publicKey,
    required String secretKey,
  })  : _dio = dio,
        _publicKey = publicKey,
        _secretKey = secretKey;

  /// Initialize payment with Khalti
  Future<Map<String, dynamic>> initiatePayment({
    required String productIdentity,
    required String productName,
    required int amount, // Amount in paisa (1 NPR = 100 paisa)
    required String productUrl,
    String? customerInfo,
  }) async {
    try {
      final response = await _dio.post(
        'https://khalti.com/api/v2/payment/initiate/',
        data: {
          'public_key': _publicKey,
          'product_identity': productIdentity,
          'product_name': productName,
          'amount': amount,
          'product_url': productUrl,
          if (customerInfo != null) 'customer_info': customerInfo,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ServerException(
          message: 'Failed to initiate payment: ${response.statusMessage}',
          code: response.statusCode.toString(),
        );
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error during payment initiation: $e',
      );
    }
  }

  /// Verify payment with Khalti
  Future<Map<String, dynamic>> verifyPayment({
    required String token,
    required int amount,
  }) async {
    try {
      final response = await _dio.post(
        'https://khalti.com/api/v2/payment/verify/',
        data: {
          'token': token,
          'amount': amount,
        },
        options: Options(
          headers: {
            'Authorization': 'Key $_secretKey',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ServerException(
          message: 'Failed to verify payment: ${response.statusMessage}',
          code: response.statusCode.toString(),
        );
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error during payment verification: $e',
      );
    }
  }

  /// Get payment details from Khalti
  Future<Map<String, dynamic>> getPaymentDetails(String paymentId) async {
    try {
      final response = await _dio.get(
        'https://khalti.com/api/v2/payment/lookup/',
        queryParameters: {
          'pidx': paymentId,
        },
        options: Options(
          headers: {
            'Authorization': 'Key $_secretKey',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ServerException(
          message: 'Failed to get payment details: ${response.statusMessage}',
          code: response.statusCode.toString(),
        );
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error while fetching payment details: $e',
      );
    }
  }

  /// Process refund through Khalti
  Future<Map<String, dynamic>> processRefund({
    required String paymentId,
    required int amount,
    String? reason,
  }) async {
    try {
      final response = await _dio.post(
        'https://khalti.com/api/v2/payment/refund/',
        data: {
          'pidx': paymentId,
          'amount': amount,
          if (reason != null) 'reason': reason,
        },
        options: Options(
          headers: {
            'Authorization': 'Key $_secretKey',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ServerException(
          message: 'Failed to process refund: ${response.statusMessage}',
          code: response.statusCode.toString(),
        );
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error during refund processing: $e',
      );
    }
  }

  /// Convert payment response to PaymentModel
  PaymentModel convertToPaymentModel({
    required Map<String, dynamic> khaltiResponse,
    required String bookingId,
    required String userId,
  }) {
    return PaymentModel(
      id: khaltiResponse['pidx'] ?? '',
      bookingId: bookingId,
      amount: ((khaltiResponse['amount'] as int?) ?? 0) /
          100.0, // Convert paisa to NPR
      currency: 'NPR',
      status: _mapKhaltiStatusToPaymentStatus(khaltiResponse['status']),
      paymentMethod: PaymentMethod.khalti,
      transactionId: khaltiResponse['transaction_id'],
      metadata: {
        'user_id': userId,
        'gateway_response': khaltiResponse,
      },
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Map Khalti payment status to our payment status
  PaymentStatus _mapKhaltiStatusToPaymentStatus(dynamic khaltiStatus) {
    switch (khaltiStatus?.toString().toLowerCase()) {
      case 'completed':
      case 'success':
        return PaymentStatus.completed;
      case 'pending':
        return PaymentStatus.pending;
      case 'failed':
      case 'error':
        return PaymentStatus.failed;
      case 'refunded':
        return PaymentStatus.refunded;
      case 'cancelled':
        return PaymentStatus.cancelled;
      default:
        return PaymentStatus.pending;
    }
  }

  /// Handle Dio errors and convert to appropriate exceptions
  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutException(
          message: 'Payment request timed out. Please try again.',
        );
      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'Network error. Please check your internet connection.',
        );
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ??
            error.response?.statusMessage ??
            'Payment failed';

        if (statusCode == 400) {
          return ValidationException(
            message: 'Invalid payment data: $message',
          );
        } else if (statusCode == 401) {
          return const UnauthorizedException(
            message: 'Payment authentication failed',
          );
        } else if (statusCode == 404) {
          return const ServerException(
            message: 'Payment service not found',
            code: '404',
          );
        } else {
          return ServerException(
            message: 'Payment failed: $message',
            code: statusCode?.toString(),
          );
        }
      case DioExceptionType.cancel:
        return const ServerException(
          message: 'Payment request was cancelled',
        );
      case DioExceptionType.unknown:
      default:
        return ServerException(
          message: 'Unknown payment error: ${error.message}',
        );
    }
  }

  /// Validate payment amount (Khalti minimum is 10 NPR = 1000 paisa)
  bool isValidAmount(int amountInPaisa) {
    return amountInPaisa >= 1000; // Minimum 10 NPR
  }

  /// Convert NPR to paisa
  static int nprToPaisa(double amountInNpr) {
    return (amountInNpr * 100).round();
  }

  /// Convert paisa to NPR
  static double paisaToNpr(int amountInPaisa) {
    return amountInPaisa / 100.0;
  }

  /// Generate unique product identity for booking
  static String generateProductIdentity(String bookingId) {
    return 'booking_$bookingId';
  }

  /// Generate product name for booking
  static String generateProductName(
      String vehicleName, DateTime startDate, DateTime endDate) {
    final startDateStr = startDate.toIso8601String().split('T')[0];
    final endDateStr = endDate.toIso8601String().split('T')[0];
    return 'Vehicle Rental: $vehicleName ($startDateStr to $endDateStr)';
  }
}
