import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';
import 'package:gaadi_sewa/features/payments/data/services/khalti_payment_service.dart';
import 'package:gaadi_sewa/features/payments/domain/repositories/payment_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PaymentRepositoryImpl implements PaymentRepository {
  final NetworkInfo networkInfo;
  final KhaltiPaymentService khaltiService;
  final SupabaseClient supabaseClient;

  PaymentRepositoryImpl({
    required this.networkInfo,
    required this.khaltiService,
    required this.supabaseClient,
  });

  @override
  Future<Either<Failure, PaymentModel>> processPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
    Map<String, dynamic>? metadata,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      if (paymentMethod.toLowerCase() == 'khalti') {
        return await _processKhaltiPayment(
          bookingId: bookingId,
          amount: amount,
          metadata: metadata,
        );
      } else {
        return Left(const ValidationFailure(
          message: 'Unsupported payment method',
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, PaymentModel>> _processKhaltiPayment({
    required String bookingId,
    required double amount,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Get booking details from Supabase
      final bookingResponse = await supabaseClient
          .from('bookings')
          .select('*, vehicles(*)')
          .eq('id', bookingId)
          .single();

      final vehicleName =
          '${bookingResponse['vehicles']['make']} ${bookingResponse['vehicles']['model']}';
      final startDate = DateTime.parse(bookingResponse['start_date']);
      final endDate = DateTime.parse(bookingResponse['end_date']);
      final userId = bookingResponse['user_id'];

      // Convert amount to paisa (Khalti uses paisa)
      final amountInPaisa = KhaltiPaymentService.nprToPaisa(amount);

      // Validate amount
      if (!khaltiService.isValidAmount(amountInPaisa)) {
        return Left(const ValidationFailure(
          message: 'Minimum payment amount is NPR 10',
        ));
      }

      // Initiate payment with Khalti
      final khaltiResponse = await khaltiService.initiatePayment(
        productIdentity:
            KhaltiPaymentService.generateProductIdentity(bookingId),
        productName: KhaltiPaymentService.generateProductName(
            vehicleName, startDate, endDate),
        amount: amountInPaisa,
        productUrl: 'https://gaadisewa.com/booking/$bookingId',
        customerInfo: metadata?['customerInfo'],
      );

      // Convert to PaymentModel
      final payment = khaltiService.convertToPaymentModel(
        khaltiResponse: khaltiResponse,
        bookingId: bookingId,
        userId: userId,
      );

      // Save payment to Supabase
      await supabaseClient.from('payments').insert(payment.toJson());

      return Right(payment);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, PaymentModel>> getPayment(String paymentId) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('payments')
          .select()
          .eq('id', paymentId)
          .single();

      final payment = PaymentModel.fromJson(response);
      return Right(payment);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(const NotFoundFailure(message: 'Payment not found'));
      }
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get payment: $e'));
    }
  }

  @override
  Future<Either<Failure, List<PaymentModel>>> getPaymentsByBooking(
      String bookingId) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('payments')
          .select()
          .eq('booking_id', bookingId)
          .order('created_at', ascending: false);

      final payments = (response as List)
          .map((json) => PaymentModel.fromJson(json))
          .toList();

      return Right(payments);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get payments: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentModel>> updatePaymentStatus({
    required String paymentId,
    required String status,
    String? transactionId,
    Map<String, dynamic>? metadata,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      final updateData = {
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
        if (transactionId != null) 'transaction_id': transactionId,
        if (metadata != null) 'gateway_response': metadata,
      };

      final response = await supabaseClient
          .from('payments')
          .update(updateData)
          .eq('id', paymentId)
          .select()
          .single();

      final payment = PaymentModel.fromJson(response);
      return Right(payment);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(const NotFoundFailure(message: 'Payment not found'));
      }
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update payment: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyPayment({
    required String paymentId,
    required String transactionId,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Get payment from database
      final paymentResult = await getPayment(paymentId);

      return paymentResult.fold(
        (failure) => Left(failure),
        (payment) async {
          // Verify payment with Khalti
          final amountInPaisa =
              KhaltiPaymentService.nprToPaisa(payment.amount.toDouble());
          final khaltiResponse = await khaltiService.verifyPayment(
            token: transactionId,
            amount: amountInPaisa,
          );

          // Update payment with verification result
          final isVerified = khaltiResponse['state']['name'] == 'Completed';

          if (isVerified) {
            await updatePaymentStatus(
              paymentId: paymentId,
              status: 'completed',
              transactionId: khaltiResponse['transaction_id'],
              metadata: khaltiResponse,
            );
          }

          return Right(isVerified);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to verify payment: $e'));
    }
  }

  Future<Either<Failure, PaymentModel>> refundPayment(
      String paymentId, double amount) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      // Get payment details
      final paymentResult = await getPayment(paymentId);

      return paymentResult.fold(
        (failure) => Left(failure),
        (payment) async {
          if (payment.status != PaymentStatus.completed) {
            return Left(const ValidationFailure(
              message: 'Only completed payments can be refunded',
            ));
          }

          // Process refund with Khalti
          final amountInPaisa = KhaltiPaymentService.nprToPaisa(amount);
          final khaltiResponse = await khaltiService.processRefund(
            paymentId: payment.transactionId ?? payment.id,
            amount: amountInPaisa,
            reason: 'Booking cancellation',
          );

          // Update payment status in Supabase
          final updatedPayment = await updatePaymentStatus(
            paymentId: paymentId,
            status: 'refunded',
            metadata: {
              ...payment.metadata ?? {},
              'refund': khaltiResponse,
            },
          );

          return updatedPayment;
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to process refund: $e'));
    }
  }

  Future<Either<Failure, List<PaymentModel>>> getPaymentHistory(
      String userId) async {
    if (!await networkInfo.isConnected) {
      return Left(const NetworkFailure(message: 'No internet connection'));
    }

    try {
      final response = await supabaseClient
          .from('payments')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final payments = (response as List)
          .map((json) => PaymentModel.fromJson(json))
          .toList();

      return Right(payments);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get payment history: $e'));
    }
  }
}
