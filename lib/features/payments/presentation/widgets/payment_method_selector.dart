import 'package:flutter/material.dart';

class PaymentMethodSelector extends StatelessWidget {
  final String selectedMethod;
  final ValueChanged<String> onMethodSelected;
  
  const PaymentMethodSelector({
    Key? key,
    required this.selectedMethod,
    required this.onMethodSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final paymentMethods = [
      _PaymentMethod(
        id: 'khalti',
        name: '<PERSON>halt<PERSON>',
        icon: 'assets/icons/khalti.png',
        color: const Color(0xFF5C2D91),
      ),
      _PaymentMethod(
        id: 'esewa',
        name: 'eSewa',
        icon: 'assets/icons/esewa.png',
        color: const Color(0xFF55A64B),
      ),
      _PaymentMethod(
        id: 'cash',
        name: 'Cash on Delivery',
        icon: 'assets/icons/cash.png',
        color: Colors.grey[700]!,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Select Payment Method',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: paymentMethods.length,
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final method = paymentMethods[index];
            return _PaymentMethodTile(
              method: method,
              isSelected: selectedMethod == method.id,
              onTap: () => onMethodSelected(method.id),
            );
          },
        ),
      ],
    );
  }
}

class _PaymentMethodTile extends StatelessWidget {
  final _PaymentMethod method;
  final bool isSelected;
  final VoidCallback onTap;

  const _PaymentMethodTile({
    Key? key,
    required this.method,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 2 : 0.5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? method.color : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: method.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Image.asset(
                  method.icon,
                  width: 24,
                  height: 24,
                  errorBuilder: (_, __, ___) => Icon(
                    Icons.payment,
                    color: method.color,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Text(
                method.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: method.color,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _PaymentMethod {
  final String id;
  final String name;
  final String icon;
  final Color color;

  _PaymentMethod({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}
