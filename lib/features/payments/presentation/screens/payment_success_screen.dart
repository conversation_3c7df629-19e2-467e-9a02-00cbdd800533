import 'package:flutter/material.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/core/widgets/primary_button.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/presentation/screens/booking_detail_screen.dart';

class PaymentSuccessScreen extends StatelessWidget {
  final BookingModel booking;
  final String paymentMethod;

  const PaymentSuccessScreen({
    Key? key,
    required this.booking,
    required this.paymentMethod,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Success Icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle_outline,
                  color: Colors.green,
                  size: 60,
                ),
              ),
              const SizedBox(height: 24),
              
              // Success Message
              const Text(
                'Payment Successful!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Booking Reference
              Text(
                'Booking #${booking.id.substring(0, 8).toUpperCase()}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 32),
              
              // Payment Details
              _buildDetailRow('Amount', 'NPR ${booking.totalAmount.toStringAsFixed(2)}'),
              _buildDetailRow('Payment Method', _formatPaymentMethod(paymentMethod)),
              _buildDetailRow('Date', '${DateTime.now().toString().split(' ')[0]}'),
              _buildDetailRow('Time', '${TimeOfDay.now().format(context)}'),
              
              const Spacer(),
              
              // Action Buttons
              PrimaryButton(
                text: 'View Booking',
                onPressed: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BookingDetailScreen(bookingId: booking.id),
                    ),
                  );
                },
              ),
              const SizedBox(height: 12),
              TextButton(
                onPressed: () {
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                child: const Text('Back to Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  String _formatPaymentMethod(String method) {
    switch (method) {
      case 'khalti':
        return 'Khalti';
      case 'esewa':
        return 'eSewa';
      case 'cash':
        return 'Cash on Delivery';
      default:
        return method;
    }
  }
}
