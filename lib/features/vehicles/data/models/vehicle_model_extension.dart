import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

/// Extension methods for [VehicleModel] to handle additional functionality
/// that's specific to the data layer

extension VehicleModelExtension on VehicleModel {
  /// Converts a VehicleModel to a JSON map for Supabase
  Map<String, dynamic> toSupabaseJson() {
    return {
      'id': id,
      'owner_id': ownerId,
      'make': make,
      'model': model,
      'year': year,
      'license_plate': licensePlate,
      'color': color,
      'type': type.toString().split('.').last.toLowerCase(),
      'transmission': transmission.toString().split('.').last.toLowerCase(),
      'fuel_type': fuelType.toString().split('.').last.toLowerCase(),
      'seating_capacity': seatingCapacity,
      'daily_rate': dailyRate,
      'hourly_rate': hourlyRate,
      'weekly_discount': weeklyDiscount,
      'monthly_discount': monthlyDiscount,
      'is_available': isAvailable,
      'description': description,
      'rules': rules,
      'location': location != null
          ? {
              'latitude': location!.latitude,
              'longitude': location!.longitude,
              'address': location!.address,
              'city': location!.city,
              'state': location!.state,
              'country': location!.country,
              'postal_code': location!.postalCode,
            }
          : null,
      'features': features?.map((f) => f.toString()).toList(),
      'images': images,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'insurance_provider': insuranceProvider,
      'insurance_policy_number': insurancePolicyNumber,
      'insurance_expiry_date': insuranceExpiryDate?.toIso8601String(),
      'inspection_date': inspectionDate?.toIso8601String(),
      'mileage': mileage,
      'engine_size': engineSize,
      'fuel_consumption': fuelConsumption,
      'doors': doors,
      'air_conditioning': airConditioning,
      'bluetooth': bluetooth,
      'gps': gps,
      'sunroof': sunroof,
      'usb_ports': usbPorts,
      'backup_camera': backupCamera,
      'parking_sensors': parkingSensors,
      'child_seat': childSeat,
      'all_wheel_drive': allWheelDrive,
      'convertible': convertible,
      'manual_transmission': manualTransmission,
      'pet_friendly': petFriendly,
      'smoking_allowed': smokingAllowed,
      'toll_pass': tollPass,
      'unlimited_mileage': unlimitedMileage,
      'delivery': delivery,
      'pickup_dropoff': pickupDropoff,
      'roadside_assistance': roadsideAssistance,
      'additional_driver': additionalDriver,
      'young_driver_surcharge': youngDriverSurcharge,
      'senior_driver_surcharge': seniorDriverSurcharge,
      'airport_fee': airportFee,
      'cleaning_fee': cleaningFee,
      'deposit': deposit,
      'excess': excess,
      'theft_protection': theftProtection,
      'tire_protection': tireProtection,
      'windshield_protection': windshieldProtection,
      'additional_insurance': additionalInsurance,
      'cancellation_policy': cancellationPolicy,
      'minimum_rental_days': minimumRentalDays,
      'maximum_rental_days': maximumRentalDays,
      'advance_notice_hours': advanceNoticeHours,
      'last_rented_at': lastRentedAt?.toIso8601String(),
      'next_available_date': nextAvailableDate?.toIso8601String(),
      'rating': rating,
      'total_ratings': totalRatings,
      'total_trips': totalTrips,
      'is_featured': isFeatured,
      'is_verified': isVerified,
      'verification_status': verificationStatus?.toString().split('.').last.toLowerCase(),
      'rejection_reason': rejectionReason,
      'status': status?.toString().split('.').last.toLowerCase(),
      'deleted_at': deletedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates a VehicleModel from Supabase JSON data
  static VehicleModel fromSupabaseJson(Map<String, dynamic> json) {
    return VehicleModel(
      id: json['id'] as String? ?? '',
      ownerId: json['owner_id'] as String? ?? '',
      make: json['make'] as String? ?? '',
      model: json['model'] as String? ?? '',
      year: json['year'] as int? ?? 0,
      licensePlate: json['license_plate'] as String? ?? '',
      color: json['color'] as String? ?? '',
      type: _parseVehicleType(json['type'] as String? ?? ''),
      transmission: _parseTransmissionType(json['transmission'] as String? ?? ''),
      fuelType: _parseFuelType(json['fuel_type'] as String? ?? ''),
      seatingCapacity: json['seating_capacity'] as int? ?? 4,
      dailyRate: (json['daily_rate'] as num?)?.toDouble() ?? 0.0,
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      weeklyDiscount: (json['weekly_discount'] as num?)?.toDouble(),
      monthlyDiscount: (json['monthly_discount'] as num?)?.toDouble(),
      isAvailable: json['is_available'] as bool? ?? false,
      description: json['description'] as String?,
      rules: json['rules'] as String?,
      location: json['location'] != null
          ? VehicleLocation(
              latitude: (json['location']['latitude'] as num).toDouble(),
              longitude: (json['location']['longitude'] as num).toDouble(),
              address: json['location']['address'] as String?,
              city: json['location']['city'] as String?,
              state: json['location']['state'] as String?,
              country: json['location']['country'] as String?,
              postalCode: json['location']['postal_code'] as String?,
            )
          : null,
      features: (json['features'] as List<dynamic>?)?.cast<String>(),
      images: (json['images'] as List<dynamic>?)?.cast<String>(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      insuranceProvider: json['insurance_provider'] as String?,
      insurancePolicyNumber: json['insurance_policy_number'] as String?,
      insuranceExpiryDate: json['insurance_expiry_date'] != null
          ? DateTime.parse(json['insurance_expiry_date'] as String)
          : null,
      inspectionDate: json['inspection_date'] != null
          ? DateTime.parse(json['inspection_date'] as String)
          : null,
      mileage: json['mileage'] as int?,
      engineSize: (json['engine_size'] as num?)?.toDouble(),
      fuelConsumption: (json['fuel_consumption'] as num?)?.toDouble(),
      doors: json['doors'] as int?,
      airConditioning: json['air_conditioning'] as bool? ?? false,
      bluetooth: json['bluetooth'] as bool? ?? false,
      gps: json['gps'] as bool? ?? false,
      sunroof: json['sunroof'] as bool? ?? false,
      usbPorts: json['usb_ports'] as bool? ?? false,
      backupCamera: json['backup_camera'] as bool? ?? false,
      parkingSensors: json['parking_sensors'] as bool? ?? false,
      childSeat: json['child_seat'] as bool? ?? false,
      allWheelDrive: json['all_wheel_drive'] as bool? ?? false,
      convertible: json['convertible'] as bool? ?? false,
      manualTransmission: json['manual_transmission'] as bool? ?? false,
      petFriendly: json['pet_friendly'] as bool? ?? false,
      smokingAllowed: json['smoking_allowed'] as bool? ?? false,
      tollPass: json['toll_pass'] as bool? ?? false,
      unlimitedMileage: json['unlimited_mileage'] as bool? ?? false,
      delivery: json['delivery'] as bool? ?? false,
      pickupDropoff: json['pickup_dropoff'] as bool? ?? false,
      roadsideAssistance: json['roadside_assistance'] as bool? ?? false,
      additionalDriver: json['additional_driver'] as bool? ?? false,
      youngDriverSurcharge: json['young_driver_surcharge'] as bool? ?? false,
      seniorDriverSurcharge: json['senior_driver_surcharge'] as bool? ?? false,
      airportFee: (json['airport_fee'] as num?)?.toDouble(),
      cleaningFee: (json['cleaning_fee'] as num?)?.toDouble(),
      deposit: (json['deposit'] as num?)?.toDouble(),
      excess: (json['excess'] as num?)?.toDouble(),
      theftProtection: (json['theft_protection'] as bool?) ?? false,
      tireProtection: (json['tire_protection'] as bool?) ?? false,
      windshieldProtection: (json['windshield_protection'] as bool?) ?? false,
      additionalInsurance: json['additional_insurance'] as bool? ?? false,
      cancellationPolicy: json['cancellation_policy'] as String?,
      minimumRentalDays: json['minimum_rental_days'] as int?,
      maximumRentalDays: json['maximum_rental_days'] as int?,
      advanceNoticeHours: json['advance_notice_hours'] as int?,
      lastRentedAt: json['last_rented_at'] != null
          ? DateTime.parse(json['last_rented_at'] as String)
          : null,
      nextAvailableDate: json['next_available_date'] != null
          ? DateTime.parse(json['next_available_date'] as String)
          : null,
      rating: (json['avg_rating'] as num?)?.toDouble() ??
          (json['rating'] as num?)?.toDouble() ??
          0.0,
      totalRatings: json['total_ratings'] as int? ?? 0,
      totalTrips: json['total_trips'] as int? ??
          (json['booking_count'] as int?) ??
          0,
      isFeatured: json['is_featured'] as bool? ?? false,
      isVerified: json['is_verified'] as bool? ?? false,
      verificationStatus: _parseVerificationStatus(
          json['verification_status'] as String? ?? ''),
      rejectionReason: json['rejection_reason'] as String?,
      status: _parseVehicleStatus(json['status'] as String? ?? ''),
      deletedAt: json['deleted_at'] != null
          ? DateTime.parse(json['deleted_at'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // Helper methods to parse enums from strings
  static VehicleType _parseVehicleType(String type) {
    switch (type.toLowerCase()) {
      case 'sedan':
        return VehicleType.sedan;
      case 'suv':
        return VehicleType.suv;
      case 'hatchback':
        return VehicleType.hatchback;
      case 'coupe':
        return VehicleType.coupe;
      case 'convertible':
        return VehicleType.convertible;
      case 'wagon':
        return VehicleType.wagon;
      case 'van':
        return VehicleType.van;
      case 'pickup':
        return VehicleType.pickup;
      case 'luxury':
        return VehicleType.luxury;
      case 'sports':
        return VehicleType.sports;
      case 'electric':
        return VehicleType.electric;
      case 'hybrid':
        return VehicleType.hybrid;
      default:
        return VehicleType.other;
    }
  }

  static TransmissionType _parseTransmissionType(String type) {
    switch (type.toLowerCase()) {
      case 'automatic':
        return TransmissionType.automatic;
      case 'manual':
        return TransmissionType.manual;
      case 'semi_automatic':
        return TransmissionType.semiAutomatic;
      case 'cvt':
        return TransmissionType.cvt;
      default:
        return TransmissionType.automatic;
    }
  }

  static FuelType _parseFuelType(String type) {
    switch (type.toLowerCase()) {
      case 'petrol':
        return FuelType.petrol;
      case 'diesel':
        return FuelType.diesel;
      case 'electric':
        return FuelType.electric;
      case 'hybrid':
        return FuelType.hybrid;
      case 'cng':
        return FuelType.cng;
      case 'lpg':
        return FuelType.lpg;
      default:
        return FuelType.petrol;
    }
  }

  static VerificationStatus _parseVerificationStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return VerificationStatus.pending;
      case 'verified':
        return VerificationStatus.verified;
      case 'rejected':
        return VerificationStatus.rejected;
      default:
        return VerificationStatus.pending;
    }
  }

  static VehicleStatus _parseVehicleStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return VehicleStatus.active;
      case 'inactive':
        return VehicleStatus.inactive;
      case 'maintenance':
        return VehicleStatus.maintenance;
      case 'rented':
        return VehicleStatus.rented;
      default:
        return VehicleStatus.active;
    }
  }
}
