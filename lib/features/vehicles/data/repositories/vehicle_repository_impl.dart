import 'dart:async';
import 'dart:developer' as developer;

import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/vehicles/data/models/vehicle_model_extension.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';
import 'package:geolocator/geolocator.dart';
import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Helper class for handling Supabase errors
class SupabaseErrorHandler {
  static Failure handleError(dynamic error) {
    developer.log('Supabase Error: $error',
        error: error, stackTrace: StackTrace.current);

    if (error is PostgrestException) {
      return ServerFailure(
        message: error.message,
        code: error.code,
      );
    } else if (error is NetworkException) {
      return NetworkFailure(message: error.message);
    } else if (error is Exception) {
      return ServerFailure(message: error.toString());
    } else {
      return const ServerFailure(message: 'An unknown error occurred');
    }
  }

  static NotFoundFailure notFound(String message) {
    return NotFoundFailure(message: message);
  }
}

@LazySingleton(as: VehicleRepository)
class VehicleRepositoryImpl implements VehicleRepository {
  final SupabaseClient _supabaseClient;
  final NetworkInfo _networkInfo;
  final StreamController<List<VehicleModel>> _vehiclesController =
      StreamController.broadcast();

  VehicleRepositoryImpl({
    required NetworkInfo networkInfo,
    @Named('supabaseClient') SupabaseClient? supabaseClient,
  })  : _networkInfo = networkInfo,
        _supabaseClient = supabaseClient ?? Supabase.instance.client;

  void dispose() {
    _vehiclesController.close();
  }

  Stream<List<VehicleModel>> get vehiclesStream => _vehiclesController.stream;

  @override
  Future<List<VehicleModel>> getVehicles({
    double? maxDistance,
    Position? userLocation,
    VehicleType? type,
    double? maxDailyRate,
    int limit = 20,
    int offset = 0,
  }) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      var queryBuilder = _supabaseClient.from('vehicles').select('''
            *,
            owner:profiles!owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            )
          ''').eq('is_available', true);

      // Apply filters
      if (type != null) {
        queryBuilder = queryBuilder.eq(
            'type', type.toString().split('.').last.toLowerCase());
      }
      if (maxDailyRate != null) {
        queryBuilder = queryBuilder.lte('daily_rate', maxDailyRate);
      }

      final response = await queryBuilder
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      // Parse the response into VehicleModel objects
      final vehicles = (response as List).map<VehicleModel>((item) {
        try {
          return VehicleModelExtension.fromSupabaseJson(item);
        } catch (e) {
          developer.log('Error parsing vehicle: $e',
              error: e, stackTrace: StackTrace.current);
          rethrow;
        }
      }).toList();

      // Apply distance filter if location is provided (client-side filtering)
      if (userLocation != null && maxDistance != null) {
        return vehicles.where((vehicle) {
          if (vehicle.location == null) return false;

          final distance = Geolocator.distanceBetween(
                userLocation.latitude,
                userLocation.longitude,
                vehicle.location!.latitude,
                vehicle.location!.longitude,
              ) /
              1000; // Convert meters to km

          return distance <= maxDistance;
        }).map((vehicle) {
          if (vehicle.location == null) return vehicle;

          final distance = Geolocator.distanceBetween(
                userLocation.latitude,
                userLocation.longitude,
                vehicle.location!.latitude,
                vehicle.location!.longitude,
              ) /
              1000; // Convert meters to km

          return vehicle.withDistance(distance);
        }).toList();
      }

      return vehicles;
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<VehicleModel?> getVehicleById(String id) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response = await _supabaseClient.from('vehicles').select('''
            *,
            owner:profiles!owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            ),
            reviews:reviews!reviews_vehicle_id_fkey(
              id,
              rating,
              comment,
              created_at,
              reviewer:profiles!reviews_reviewer_id_fkey(
                id,
                full_name,
                avatar_url
              )
            ),
            (SELECT AVG(rating)::numeric(10,1) FROM reviews WHERE vehicle_id = vehicles.id) as avg_rating,
            (SELECT COUNT(*) FROM bookings WHERE vehicle_id = vehicles.id) as booking_count
          ''').eq('id', id).single();

      return VehicleModelExtension.fromSupabaseJson(response);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        // Not found
        return null;
      }
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<VehicleModel>> getVehiclesByOwner(String ownerId) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response = await _supabaseClient.from('vehicles').select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url
            )
          ''').eq('owner_id', ownerId).order('created_at', ascending: false);

      return (response as List)
          .map((json) => VehicleModelExtension.fromSupabaseJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<VehicleModel>> getVehiclesAdvanced(
      VehicleFilterModel filter) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      var queryBuilder = _supabaseClient
          .from('vehicles')
          .select('*, profiles:owner_id(*)')
          .eq('is_available', true);

      // Apply filters
      if (filter.vehicleType != null) {
        queryBuilder = queryBuilder.eq('type',
            filter.vehicleType.toString().split('.').last.toLowerCase());
      }

      if (filter.minDailyRate != null) {
        queryBuilder = queryBuilder.gte('daily_rate', filter.minDailyRate!);
      }

      if (filter.maxDailyRate != null) {
        queryBuilder = queryBuilder.lte('daily_rate', filter.maxDailyRate!);
      }

      if (filter.make != null && filter.make!.isNotEmpty) {
        queryBuilder = queryBuilder.ilike('make', '%${filter.make}%');
      }

      if (filter.model != null && filter.model!.isNotEmpty) {
        queryBuilder = queryBuilder.ilike('model', '%${filter.model}%');
      }

      // Apply year filters
      if (filter.minYear != null) {
        queryBuilder = queryBuilder.gte('year', filter.minYear.toString());
      }
      if (filter.maxYear != null) {
        queryBuilder = queryBuilder.lte('year', filter.maxYear.toString());
      }

      // Apply text search if provided
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        String searchTerm = '%${filter.searchQuery!}%';
        queryBuilder = queryBuilder.or(
            'make.like.$searchTerm,model.like.$searchTerm,description.like.$searchTerm');
      }

      // Apply sorting
      if (filter.sortOrder != null) {
        switch (filter.sortOrder!) {
          case VehicleSortOrder.newest:
            // Will be sorted client-side
            break;
          case VehicleSortOrder.oldest:
            // Will be sorted client-side
            break;
          case VehicleSortOrder.priceAsc:
            // Will be sorted client-side
            break;
          case VehicleSortOrder.priceDesc:
            // Will be sorted client-side
            break;
          case VehicleSortOrder.highestRated:
            // Will be sorted client-side
            break;
          case VehicleSortOrder.mostPopular:
            // Will be sorted client-side
            break;
          case VehicleSortOrder.nearest:
            // Nearest will be handled client-side
            break;
        }
      } else {
        // Default sort by newest
        // Default sort will be applied below
      }

      final response = await queryBuilder
          .order('created_at', ascending: false)
          .range(filter.offset, filter.offset + filter.limit - 1);

      List<VehicleModel> vehicles = (response as List)
          .map((json) => VehicleModelExtension.fromSupabaseJson(json))
          .toList();

      // Apply client-side filters that couldn't be done with Supabase queries
      vehicles = vehicles.where((vehicle) {
        // Distance filter
        if (filter.userLocation != null && filter.maxDistance != null) {
          if (vehicle.location == null) return false;

          final distance = Geolocator.distanceBetween(
                filter.userLocation!.latitude,
                filter.userLocation!.longitude,
                vehicle.location!.latitude,
                vehicle.location!.longitude,
              ) /
              1000; // Convert meters to km

          if (distance > filter.maxDistance!) {
            return false;
          }
        }

        // Feature filters
        if (filter.requiredFeatures != null &&
            filter.requiredFeatures!.isNotEmpty) {
          if (vehicle.features == null) return false;

          for (final feature in filter.requiredFeatures!) {
            if (!vehicle.features!.contains(feature)) {
              return false;
            }
          }
        }

        return true;
      }).toList();

      // Add distance information to vehicles if user location is provided
      if (filter.userLocation != null) {
        vehicles = vehicles.map((vehicle) {
          if (vehicle.location == null) return vehicle;

          final distance = Geolocator.distanceBetween(
                filter.userLocation!.latitude,
                filter.userLocation!.longitude,
                vehicle.location!.latitude,
                vehicle.location!.longitude,
              ) /
              1000; // Convert meters to km

          return vehicle.withDistance(distance);
        }).toList();
      }

      // Apply client-side sorting
      if (filter.sortOrder != null) {
        switch (filter.sortOrder!) {
          case VehicleSortOrder.newest:
            vehicles.sort((a, b) => (b.createdAt ?? DateTime.now())
                .compareTo(a.createdAt ?? DateTime.now()));
            break;
          case VehicleSortOrder.oldest:
            vehicles.sort((a, b) => (a.createdAt ?? DateTime.now())
                .compareTo(b.createdAt ?? DateTime.now()));
            break;
          case VehicleSortOrder.priceAsc:
            vehicles.sort((a, b) => a.dailyRate.compareTo(b.dailyRate));
            break;
          case VehicleSortOrder.priceDesc:
            vehicles.sort((a, b) => b.dailyRate.compareTo(a.dailyRate));
            break;
          case VehicleSortOrder.highestRated:
            vehicles.sort((a, b) => b.rating.compareTo(a.rating));
            break;
          case VehicleSortOrder.mostPopular:
            vehicles.sort((a, b) => b.totalTrips.compareTo(a.totalTrips));
            break;
          case VehicleSortOrder.nearest:
            if (filter.userLocation != null) {
              vehicles.sort((a, b) => (a.distanceFromUser ?? double.infinity)
                  .compareTo(b.distanceFromUser ?? double.infinity));
            }
            break;
        }
      }

      return vehicles;
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<String>> getAvailableVehicleMakes() async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('make')
          .eq('is_available', true)
          .order('make');

      // Extract unique makes
      Set<String> uniqueMakes = {};
      for (var item in response) {
        uniqueMakes.add(item['make'] as String);
      }

      return uniqueMakes.toList();
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<String>> getAvailableVehicleModels(String make) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('model')
          .eq('is_available', true)
          .eq('make', make)
          .order('model');

      // Extract unique models
      Set<String> uniqueModels = {};
      for (var item in response) {
        uniqueModels.add(item['model'] as String);
      }

      return uniqueModels.toList();
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<String>> getAvailableVehicleFeatures() async {
    try {
      // First try to get features from a dedicated features table if it exists
      try {
        final response = await _supabaseClient
            .from('vehicle_features')
            .select('name')
            .order('name', ascending: true);

        if (response.isNotEmpty) {
          return response
              .where(
                  (e) => e['name'] != null && e['name'].toString().isNotEmpty)
              .map((e) => e['name'].toString().trim())
              .toSet()
              .toList()
            ..sort((a, b) => a.compareTo(b));
        }
      } catch (_) {
        // If the features table doesn't exist or there's an error, fall back to the static list
      }

      // Fallback to a static list of common features
      return [
        'Air Conditioning',
        'Bluetooth',
        'GPS',
        'USB Port',
        'Sunroof',
        'Leather Seats',
        'Heated Seats',
        'Backup Camera',
        'Blind Spot Monitor',
        'Parking Sensors',
        'Keyless Entry',
        'Push Button Start',
        'Apple CarPlay',
        'Android Auto',
        'WiFi Hotspot',
        'Bike Rack',
        'Roof Rack',
        'Pet Friendly',
        'Wheelchair Accessible',
      ];
    } catch (e) {
      // If everything fails, log the error but still return the default list
      developer.log('Error fetching vehicle features: $e');
      return [];
    }
  }

  @override
  Future<VehicleModel> createVehicle(VehicleModel vehicle) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      // Convert the vehicle to JSON and remove any null values
      final vehicleJson = vehicle.toJson()
        ..removeWhere((key, value) => value == null);

      final response =
          await _supabaseClient.from('vehicles').insert(vehicleJson).select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            )
          ''').single();

      return VehicleModelExtension.fromSupabaseJson(response);
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<VehicleModel> updateVehicle(VehicleModel vehicle) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      // Convert the vehicle to JSON and remove any null values
      final vehicleJson = vehicle.toJson()
        ..removeWhere((key, value) => value == null)
        ..remove('id') // Don't update the ID
        ..remove('owner_id') // Don't update the owner
        ..remove('created_at'); // Don't update the creation date

      final response = await _supabaseClient
          .from('vehicles')
          .update(vehicleJson)
          .eq('id', vehicle.id)
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            )
          ''').single();

      return VehicleModelExtension.fromSupabaseJson(response);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw const NotFoundFailure(message: 'Vehicle not found');
      }
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<void> deleteVehicle(String id) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response =
          await _supabaseClient.from('vehicles').delete().eq('id', id);

      if (response == null || (response is List && response.isEmpty)) {
        throw const NotFoundFailure(message: 'Vehicle not found');
      }
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw const NotFoundFailure(message: 'Vehicle not found');
      }
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<bool> toggleAvailability(String id, bool isAvailable) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response = await _supabaseClient
          .from('vehicles')
          .update({
            'is_available': isAvailable,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', id)
          .select('is_available')
          .single();

      // Note: Supabase single() throws an exception if no rows found, so we don't need to check for null

      return response['is_available'] as bool;
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw const NotFoundFailure(message: 'Vehicle not found');
      }
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<VehicleModel>> searchVehicles(String query,
      {int limit = 20, int offset = 0}) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException(message: 'No internet connection');
    }

    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('*, profiles:owner_id(*)')
          .textSearch('fts', query, config: 'english')
          .order('created_at', ascending: false)
          .limit(limit)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => VehicleModelExtension.fromSupabaseJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }
}
