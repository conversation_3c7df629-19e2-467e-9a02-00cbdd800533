import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

/// Model representing advanced search and filter options for vehicles
class VehicleFilterModel extends Equatable {
  /// Filter by vehicle type
  final VehicleType? vehicleType;
  
  /// Filter by minimum price per day
  final double? minDailyRate;
  
  /// Filter by maximum price per day
  final double? maxDailyRate;
  
  /// Filter by minimum price per hour
  final double? minHourlyRate;
  
  /// Filter by maximum price per hour
  final double? maxHourlyRate;
  
  /// Filter by vehicle make (manufacturer)
  final String? make;
  
  /// Filter by vehicle model
  final String? model;
  
  /// Filter by minimum year of manufacture
  final int? minYear;
  
  /// Filter by maximum year of manufacture
  final int? maxYear;
  
  /// Filter by user's current location
  final Position? userLocation;
  
  /// Maximum distance from user's location in kilometers
  final double? maxDistance;
  
  /// Filter by vehicle availability from this date
  final DateTime? availableFrom;
  
  /// Filter by vehicle availability until this date
  final DateTime? availableUntil;
  
  /// Filter by specific vehicle features
  final List<String>? requiredFeatures;
  
  /// Search query for text-based search
  final String? searchQuery;
  
  /// Sort order for results
  final VehicleSortOrder? sortOrder;
  
  /// Number of results to return
  final int limit;
  
  /// Offset for pagination
  final int offset;

  const VehicleFilterModel({
    this.vehicleType,
    this.minDailyRate,
    this.maxDailyRate,
    this.minHourlyRate,
    this.maxHourlyRate,
    this.make,
    this.model,
    this.minYear,
    this.maxYear,
    this.userLocation,
    this.maxDistance,
    this.availableFrom,
    this.availableUntil,
    this.requiredFeatures,
    this.searchQuery,
    this.sortOrder,
    this.limit = 20,
    this.offset = 0,
  });

  /// Creates a copy of this filter with the given fields replaced with the new values
  VehicleFilterModel copyWith({
    VehicleType? vehicleType,
    double? minDailyRate,
    double? maxDailyRate,
    double? minHourlyRate,
    double? maxHourlyRate,
    String? make,
    String? model,
    int? minYear,
    int? maxYear,
    Position? userLocation,
    double? maxDistance,
    DateTime? availableFrom,
    DateTime? availableUntil,
    List<String>? requiredFeatures,
    String? searchQuery,
    VehicleSortOrder? sortOrder,
    int? limit,
    int? offset,
    bool clearVehicleType = false,
    bool clearMinDailyRate = false,
    bool clearMaxDailyRate = false,
    bool clearMinHourlyRate = false,
    bool clearMaxHourlyRate = false,
    bool clearMake = false,
    bool clearModel = false,
    bool clearMinYear = false,
    bool clearMaxYear = false,
    bool clearUserLocation = false,
    bool clearMaxDistance = false,
    bool clearAvailableFrom = false,
    bool clearAvailableUntil = false,
    bool clearRequiredFeatures = false,
    bool clearSearchQuery = false,
    bool clearSortOrder = false,
  }) {
    return VehicleFilterModel(
      vehicleType: clearVehicleType ? null : vehicleType ?? this.vehicleType,
      minDailyRate: clearMinDailyRate ? null : minDailyRate ?? this.minDailyRate,
      maxDailyRate: clearMaxDailyRate ? null : maxDailyRate ?? this.maxDailyRate,
      minHourlyRate: clearMinHourlyRate ? null : minHourlyRate ?? this.minHourlyRate,
      maxHourlyRate: clearMaxHourlyRate ? null : maxHourlyRate ?? this.maxHourlyRate,
      make: clearMake ? null : make ?? this.make,
      model: clearModel ? null : model ?? this.model,
      minYear: clearMinYear ? null : minYear ?? this.minYear,
      maxYear: clearMaxYear ? null : maxYear ?? this.maxYear,
      userLocation: clearUserLocation ? null : userLocation ?? this.userLocation,
      maxDistance: clearMaxDistance ? null : maxDistance ?? this.maxDistance,
      availableFrom: clearAvailableFrom ? null : availableFrom ?? this.availableFrom,
      availableUntil: clearAvailableUntil ? null : availableUntil ?? this.availableUntil,
      requiredFeatures: clearRequiredFeatures ? null : requiredFeatures ?? this.requiredFeatures,
      searchQuery: clearSearchQuery ? null : searchQuery ?? this.searchQuery,
      sortOrder: clearSortOrder ? null : sortOrder ?? this.sortOrder,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Converts this filter to a map for use in API requests
  Map<String, dynamic> toMap() {
    return {
      'vehicleType': vehicleType,
      'minDailyRate': minDailyRate,
      'maxDailyRate': maxDailyRate,
      'minHourlyRate': minHourlyRate,
      'maxHourlyRate': maxHourlyRate,
      'make': make,
      'model': model,
      'minYear': minYear,
      'maxYear': maxYear,
      'userLocation': userLocation,
      'maxDistance': maxDistance,
      'availableFrom': availableFrom?.toIso8601String(),
      'availableUntil': availableUntil?.toIso8601String(),
      'requiredFeatures': requiredFeatures,
      'searchQuery': searchQuery,
      'sortOrder': sortOrder?.name,
      'limit': limit,
      'offset': offset,
    };
  }

  /// Creates a new filter with all fields reset to null
  VehicleFilterModel clear() {
    return const VehicleFilterModel();
  }

  /// Determines if this filter has any active filters
  bool get hasActiveFilters {
    return vehicleType != null ||
        minDailyRate != null ||
        maxDailyRate != null ||
        minHourlyRate != null ||
        maxHourlyRate != null ||
        make != null ||
        model != null ||
        minYear != null ||
        maxYear != null ||
        userLocation != null ||
        maxDistance != null ||
        availableFrom != null ||
        availableUntil != null ||
        (requiredFeatures != null && requiredFeatures!.isNotEmpty) ||
        (searchQuery != null && searchQuery!.isNotEmpty);
  }

  /// Returns the number of active filters
  int get activeFilterCount {
    int count = 0;
    if (vehicleType != null) count++;
    if (minDailyRate != null || maxDailyRate != null) count++;
    if (minHourlyRate != null || maxHourlyRate != null) count++;
    if (make != null) count++;
    if (model != null) count++;
    if (minYear != null || maxYear != null) count++;
    if (userLocation != null && maxDistance != null) count++;
    if (availableFrom != null || availableUntil != null) count++;
    if (requiredFeatures != null && requiredFeatures!.isNotEmpty) count++;
    if (searchQuery != null && searchQuery!.isNotEmpty) count++;
    return count;
  }

  @override
  List<Object?> get props => [
        vehicleType,
        minDailyRate,
        maxDailyRate,
        minHourlyRate,
        maxHourlyRate,
        make,
        model,
        minYear,
        maxYear,
        userLocation,
        maxDistance,
        availableFrom,
        availableUntil,
        requiredFeatures,
        searchQuery,
        sortOrder,
        limit,
        offset,
      ];
}

/// Enum representing different sort orders for vehicle search results
enum VehicleSortOrder {
  /// Sort by newest first (default)
  newest,
  
  /// Sort by oldest first
  oldest,
  
  /// Sort by price (lowest first)
  priceAsc,
  
  /// Sort by price (highest first)
  priceDesc,
  
  /// Sort by distance (nearest first)
  nearest,
  
  /// Sort by rating (highest first)
  highestRated,
  
  /// Sort by popularity (most booked)
  mostPopular,
}
