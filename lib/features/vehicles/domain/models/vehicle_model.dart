import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'vehicle_model.g.dart';

enum VehicleType {
  bicycle,
  scooter,
  car,
  sedan,
  suv,
  hatchback,
  coupe,
  convertible,
  wagon,
  van,
  pickup,
  luxury,
  sports,
  electric,
  hybrid,
  other
}

enum TransmissionType { manual, automatic, cvt, semiAutomatic }

enum FuelType { petrol, diesel, electric, hybrid, cng, lpg }

enum VehicleStatus { active, inactive, maintenance, pending, rented }

enum VerificationStatus { pending, verified, rejected }

/// Vehicle location model
@JsonSerializable()
class VehicleLocation extends Equatable {
  final double latitude;
  final double longitude;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  const VehicleLocation({
    required this.latitude,
    required this.longitude,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  factory VehicleLocation.fromJson(Map<String, dynamic> json) =>
      _$VehicleLocationFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleLocationToJson(this);

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        address,
        city,
        state,
        country,
        postalCode,
      ];
}

@JsonSerializable()
class VehicleModel extends Equatable {
  final String id;
  final String ownerId;
  final VehicleType type;
  final String make;
  final String model;
  final int year;
  final String licensePlate;
  final String color;
  final TransmissionType transmission;
  final FuelType fuelType;
  final int seatingCapacity;
  final double dailyRate;
  final double? hourlyRate;
  final double? weeklyDiscount;
  final double? monthlyDiscount;
  final bool isAvailable;
  final String? description;
  final String? rules;
  final VehicleLocation? location;
  final List<String>? features;
  final List<String>? images;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // Insurance and inspection
  final String? insuranceProvider;
  final String? insurancePolicyNumber;
  final DateTime? insuranceExpiryDate;
  final DateTime? inspectionDate;

  // Vehicle specifications
  final int? mileage;
  final double? engineSize;
  final double? fuelConsumption;
  final int? doors;

  // Features (boolean flags)
  final bool airConditioning;
  final bool bluetooth;
  final bool gps;
  final bool sunroof;
  final bool usbPorts;
  final bool backupCamera;
  final bool parkingSensors;
  final bool childSeat;
  final bool allWheelDrive;
  final bool convertible;
  final bool manualTransmission;
  final bool petFriendly;
  final bool smokingAllowed;
  final bool tollPass;
  final bool unlimitedMileage;
  final bool delivery;
  final bool pickupDropoff;
  final bool roadsideAssistance;
  final bool additionalDriver;
  final bool youngDriverSurcharge;
  final bool seniorDriverSurcharge;

  // Fees
  final double? airportFee;
  final double? cleaningFee;
  final double? deposit;
  final double? excess;

  // Protection
  final bool theftProtection;
  final bool tireProtection;
  final bool windshieldProtection;
  final bool additionalInsurance;

  // Policies
  final String? cancellationPolicy;
  final int? minimumRentalDays;
  final int? maximumRentalDays;
  final int? advanceNoticeHours;

  // Rental history
  final DateTime? lastRentedAt;
  final DateTime? nextAvailableDate;

  // Rating and statistics
  final double rating;
  final int totalRatings;
  final int totalTrips;

  // Status and verification
  final bool isFeatured;
  final bool isVerified;
  final VerificationStatus? verificationStatus;
  final String? rejectionReason;
  final VehicleStatus? status;
  final DateTime? deletedAt;

  // Metadata
  final Map<String, dynamic>? metadata;

  /// Distance from user's location in kilometers (not stored in database)
  /// Used for distance-based filtering and sorting
  @JsonKey(includeFromJson: false, includeToJson: false)
  final double? distanceFromUser;

  /// Average rating (computed from reviews)
  @JsonKey(includeFromJson: false, includeToJson: false)
  double? get avgRating => rating > 0 ? rating : null;

  /// Booking count (computed from bookings)
  @JsonKey(includeFromJson: false, includeToJson: false)
  int? get bookingCount => totalTrips;

  const VehicleModel({
    required this.id,
    required this.ownerId,
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.licensePlate,
    required this.color,
    required this.transmission,
    required this.fuelType,
    required this.seatingCapacity,
    required this.dailyRate,
    this.hourlyRate,
    this.weeklyDiscount,
    this.monthlyDiscount,
    this.isAvailable = true,
    this.description,
    this.rules,
    this.location,
    this.features,
    this.images,
    this.createdAt,
    this.updatedAt,
    this.insuranceProvider,
    this.insurancePolicyNumber,
    this.insuranceExpiryDate,
    this.inspectionDate,
    this.mileage,
    this.engineSize,
    this.fuelConsumption,
    this.doors,
    this.airConditioning = false,
    this.bluetooth = false,
    this.gps = false,
    this.sunroof = false,
    this.usbPorts = false,
    this.backupCamera = false,
    this.parkingSensors = false,
    this.childSeat = false,
    this.allWheelDrive = false,
    this.convertible = false,
    this.manualTransmission = false,
    this.petFriendly = false,
    this.smokingAllowed = false,
    this.tollPass = false,
    this.unlimitedMileage = false,
    this.delivery = false,
    this.pickupDropoff = false,
    this.roadsideAssistance = false,
    this.additionalDriver = false,
    this.youngDriverSurcharge = false,
    this.seniorDriverSurcharge = false,
    this.airportFee,
    this.cleaningFee,
    this.deposit,
    this.excess,
    this.theftProtection = false,
    this.tireProtection = false,
    this.windshieldProtection = false,
    this.additionalInsurance = false,
    this.cancellationPolicy,
    this.minimumRentalDays,
    this.maximumRentalDays,
    this.advanceNoticeHours,
    this.lastRentedAt,
    this.nextAvailableDate,
    this.rating = 0.0,
    this.totalRatings = 0,
    this.totalTrips = 0,
    this.isFeatured = false,
    this.isVerified = false,
    this.verificationStatus,
    this.rejectionReason,
    this.status,
    this.deletedAt,
    this.metadata,
    this.distanceFromUser,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) =>
      _$VehicleModelFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleModelToJson(this);

  VehicleModel copyWith({
    String? id,
    String? ownerId,
    VehicleType? type,
    String? make,
    String? model,
    int? year,
    String? licensePlate,
    String? color,
    TransmissionType? transmission,
    FuelType? fuelType,
    int? seatingCapacity,
    double? dailyRate,
    double? hourlyRate,
    double? weeklyDiscount,
    double? monthlyDiscount,
    bool? isAvailable,
    String? description,
    String? rules,
    VehicleLocation? location,
    List<String>? features,
    List<String>? images,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? distanceFromUser,
    String? insuranceProvider,
    String? insurancePolicyNumber,
    DateTime? insuranceExpiryDate,
    DateTime? inspectionDate,
    int? mileage,
    double? engineSize,
    double? fuelConsumption,
    int? doors,
    bool? airConditioning,
    bool? bluetooth,
    bool? gps,
    bool? sunroof,
    bool? usbPorts,
    bool? backupCamera,
    bool? parkingSensors,
    bool? childSeat,
    bool? allWheelDrive,
    bool? convertible,
    bool? manualTransmission,
    bool? petFriendly,
    bool? smokingAllowed,
    bool? tollPass,
    bool? unlimitedMileage,
    bool? delivery,
    bool? pickupDropoff,
    bool? roadsideAssistance,
    bool? additionalDriver,
    bool? youngDriverSurcharge,
    bool? seniorDriverSurcharge,
    double? airportFee,
    double? cleaningFee,
    double? deposit,
    double? excess,
    bool? theftProtection,
    bool? tireProtection,
    bool? windshieldProtection,
    bool? additionalInsurance,
    String? cancellationPolicy,
    int? minimumRentalDays,
    int? maximumRentalDays,
    int? advanceNoticeHours,
    DateTime? lastRentedAt,
    DateTime? nextAvailableDate,
    double? rating,
    int? totalRatings,
    int? totalTrips,
    bool? isFeatured,
    bool? isVerified,
    VerificationStatus? verificationStatus,
    String? rejectionReason,
    VehicleStatus? status,
    DateTime? deletedAt,
    Map<String, dynamic>? metadata,
  }) {
    return VehicleModel(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      type: type ?? this.type,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      licensePlate: licensePlate ?? this.licensePlate,
      color: color ?? this.color,
      transmission: transmission ?? this.transmission,
      fuelType: fuelType ?? this.fuelType,
      seatingCapacity: seatingCapacity ?? this.seatingCapacity,
      dailyRate: dailyRate ?? this.dailyRate,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      weeklyDiscount: weeklyDiscount ?? this.weeklyDiscount,
      monthlyDiscount: monthlyDiscount ?? this.monthlyDiscount,
      isAvailable: isAvailable ?? this.isAvailable,
      description: description ?? this.description,
      rules: rules ?? this.rules,
      location: location ?? this.location,
      features: features ?? this.features,
      images: images ?? this.images,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      insuranceProvider: insuranceProvider ?? this.insuranceProvider,
      insurancePolicyNumber:
          insurancePolicyNumber ?? this.insurancePolicyNumber,
      insuranceExpiryDate: insuranceExpiryDate ?? this.insuranceExpiryDate,
      inspectionDate: inspectionDate ?? this.inspectionDate,
      mileage: mileage ?? this.mileage,
      engineSize: engineSize ?? this.engineSize,
      fuelConsumption: fuelConsumption ?? this.fuelConsumption,
      doors: doors ?? this.doors,
      airConditioning: airConditioning ?? this.airConditioning,
      bluetooth: bluetooth ?? this.bluetooth,
      gps: gps ?? this.gps,
      sunroof: sunroof ?? this.sunroof,
      usbPorts: usbPorts ?? this.usbPorts,
      backupCamera: backupCamera ?? this.backupCamera,
      parkingSensors: parkingSensors ?? this.parkingSensors,
      childSeat: childSeat ?? this.childSeat,
      allWheelDrive: allWheelDrive ?? this.allWheelDrive,
      convertible: convertible ?? this.convertible,
      manualTransmission: manualTransmission ?? this.manualTransmission,
      petFriendly: petFriendly ?? this.petFriendly,
      smokingAllowed: smokingAllowed ?? this.smokingAllowed,
      tollPass: tollPass ?? this.tollPass,
      unlimitedMileage: unlimitedMileage ?? this.unlimitedMileage,
      delivery: delivery ?? this.delivery,
      pickupDropoff: pickupDropoff ?? this.pickupDropoff,
      roadsideAssistance: roadsideAssistance ?? this.roadsideAssistance,
      additionalDriver: additionalDriver ?? this.additionalDriver,
      youngDriverSurcharge: youngDriverSurcharge ?? this.youngDriverSurcharge,
      seniorDriverSurcharge:
          seniorDriverSurcharge ?? this.seniorDriverSurcharge,
      airportFee: airportFee ?? this.airportFee,
      cleaningFee: cleaningFee ?? this.cleaningFee,
      deposit: deposit ?? this.deposit,
      excess: excess ?? this.excess,
      theftProtection: theftProtection ?? this.theftProtection,
      tireProtection: tireProtection ?? this.tireProtection,
      windshieldProtection: windshieldProtection ?? this.windshieldProtection,
      additionalInsurance: additionalInsurance ?? this.additionalInsurance,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      minimumRentalDays: minimumRentalDays ?? this.minimumRentalDays,
      maximumRentalDays: maximumRentalDays ?? this.maximumRentalDays,
      advanceNoticeHours: advanceNoticeHours ?? this.advanceNoticeHours,
      lastRentedAt: lastRentedAt ?? this.lastRentedAt,
      nextAvailableDate: nextAvailableDate ?? this.nextAvailableDate,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      totalTrips: totalTrips ?? this.totalTrips,
      isFeatured: isFeatured ?? this.isFeatured,
      isVerified: isVerified ?? this.isVerified,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      status: status ?? this.status,
      deletedAt: deletedAt ?? this.deletedAt,
      metadata: metadata ?? this.metadata,
      distanceFromUser: distanceFromUser ?? this.distanceFromUser,
    );
  }

  @override
  List<Object?> get props => [
        id,
        ownerId,
        type,
        make,
        model,
        year,
        licensePlate,
        color,
        transmission,
        fuelType,
        seatingCapacity,
        dailyRate,
        hourlyRate,
        weeklyDiscount,
        monthlyDiscount,
        isAvailable,
        description,
        rules,
        location,
        features,
        images,
        createdAt,
        updatedAt,
        insuranceProvider,
        insurancePolicyNumber,
        insuranceExpiryDate,
        inspectionDate,
        mileage,
        engineSize,
        fuelConsumption,
        doors,
        airConditioning,
        bluetooth,
        gps,
        sunroof,
        usbPorts,
        backupCamera,
        parkingSensors,
        childSeat,
        allWheelDrive,
        convertible,
        manualTransmission,
        petFriendly,
        smokingAllowed,
        tollPass,
        unlimitedMileage,
        delivery,
        pickupDropoff,
        roadsideAssistance,
        additionalDriver,
        youngDriverSurcharge,
        seniorDriverSurcharge,
        airportFee,
        cleaningFee,
        deposit,
        excess,
        theftProtection,
        tireProtection,
        windshieldProtection,
        additionalInsurance,
        cancellationPolicy,
        minimumRentalDays,
        maximumRentalDays,
        advanceNoticeHours,
        lastRentedAt,
        nextAvailableDate,
        rating,
        totalRatings,
        totalTrips,
        isFeatured,
        isVerified,
        verificationStatus,
        rejectionReason,
        status,
        deletedAt,
        metadata,
        distanceFromUser,
      ];

  /// Create a copy with distance from user
  VehicleModel withDistance(double distance) {
    return copyWith(distanceFromUser: distance);
  }
}
