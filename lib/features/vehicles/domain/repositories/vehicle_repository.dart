import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:geolocator/geolocator.dart';

abstract class VehicleRepository {
  /// Fetches a list of available vehicles with basic filters (legacy method)
  Future<List<VehicleModel>> getVehicles({
    double? maxDistance,
    Position? userLocation,
    VehicleType? type,
    double? maxDailyRate,
    int limit = 20,
    int offset = 0,
  });
  
  /// Fetches a list of available vehicles with advanced filters
  Future<List<VehicleModel>> getVehiclesAdvanced(VehicleFilterModel filter);
  
  /// Searches for vehicles by text query
  Future<List<VehicleModel>> searchVehicles(String query, {int limit = 20, int offset = 0});
  
  /// Gets common vehicle makes available in the system
  Future<List<String>> getAvailableVehicleMakes();
  
  /// Gets common vehicle models for a specific make
  Future<List<String>> getAvailableVehicleModels(String make);
  
  /// Gets all available vehicle features for filtering
  Future<List<String>> getAvailableVehicleFeatures();

  /// Fetches a single vehicle by ID
  Future<VehicleModel?> getVehicleById(String id);

  /// Fetches vehicles owned by a specific user
  Future<List<VehicleModel>> getVehiclesByOwner(String ownerId);

  /// Creates a new vehicle listing
  Future<VehicleModel> createVehicle(VehicleModel vehicle);

  /// Updates an existing vehicle listing
  Future<VehicleModel> updateVehicle(VehicleModel vehicle);

  /// Deletes a vehicle listing
  Future<void> deleteVehicle(String id);



  /// Toggles the availability of a vehicle
  Future<bool> toggleAvailability(String id, bool isAvailable);
}
