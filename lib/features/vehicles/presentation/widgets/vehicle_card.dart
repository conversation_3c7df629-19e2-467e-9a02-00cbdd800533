import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_summary_widget.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

class VehicleCard extends ConsumerWidget {
  final VehicleModel vehicle;
  final VoidCallback? onTap;

  const VehicleCard({
    Key? key,
    required this.vehicle,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Vehicle Image
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: vehicle.images?.isNotEmpty == true
                    ? CachedNetworkImage(
                        imageUrl: vehicle.images!.first,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[200],
                          child: const Icon(Icons.directions_car, size: 48),
                        ),
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.directions_car, size: 48),
                      ),
              ),
            ),

            // Vehicle Details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${vehicle.year} ${vehicle.make} ${vehicle.model}',
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            // Add review rating badge
                            ReviewRatingBadge(
                              vehicleId: vehicle.id,
                              height: 20,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Rs. ${vehicle.dailyRate.toStringAsFixed(0)}/day',
                        style: textTheme.titleSmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Type and Location
                  Row(
                    children: [
                      _buildInfoChip(
                        context,
                        icon: Icons.directions_car,
                        label: _formatVehicleType(vehicle.type),
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        context,
                        icon: Icons.location_on,
                        label: 'Nearby', // TODO: Calculate distance
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Features (if any)
                  if (vehicle.features?.isNotEmpty == true) ...[
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: vehicle.features!
                          .take(3)
                          .map((feature) => Chip(
                                label: Text(
                                  feature,
                                  style: textTheme.labelSmall,
                                ),
                                padding: EdgeInsets.zero,
                                visualDensity: VisualDensity.compact,
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                backgroundColor:
                                    theme.colorScheme.surfaceVariant,
                              ))
                          .toList(),
                    ),
                    const SizedBox(height: 4),
                  ],

                  // Availability
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: vehicle.isAvailable
                              ? Colors.green
                              : Colors.orange,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        vehicle.isAvailable ? 'Available' : 'Not Available',
                        style: textTheme.bodySmall?.copyWith(
                          color: vehicle.isAvailable
                              ? Colors.green
                              : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context,
      {required IconData icon, required String label}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.labelSmall,
          ),
        ],
      ),
    );
  }

  String _formatVehicleType(VehicleType type) {
    return type.toString().split('.').last[0].toUpperCase() +
        type.toString().split('.').last.substring(1);
  }
}
