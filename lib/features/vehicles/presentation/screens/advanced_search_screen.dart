import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/providers/vehicle_provider.dart';
import 'package:gaadi_sewa/core/presentation/widgets/app_loading_indicator.dart';
import 'package:gaadi_sewa/core/presentation/widgets/app_error_widget.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';

class AdvancedSearchScreen extends ConsumerStatefulWidget {
  static const routeName = '/advanced-search';

  const AdvancedSearchScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends ConsumerState<AdvancedSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final RangeValues _dailyPriceRange = const RangeValues(500, 10000);
  final RangeValues _yearRange = RangeValues(
    DateTime.now().year - 20,
    DateTime.now().year.toDouble(),
  );
  
  String? _selectedMake;
  List<String> _selectedFeatures = [];
  DateTime? _availableFrom;
  DateTime? _availableUntil;
  VehicleSortOrder _sortOrder = VehicleSortOrder.newest;
  double _distanceRadius = 10.0; // Default 10 km
  bool _isLoadingLocation = false;
  Position? _userPosition;

  @override
  void initState() {
    super.initState();
    
    // Initialize the search controller with any existing search query
    final filter = ref.read(vehicleFilterProvider);
    if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
      _searchController.text = filter.searchQuery!;
    }
    
    // Load current location
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLoadingLocation = true);
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() => _userPosition = position);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not get current location')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingLocation = false);
      }
    }
  }

  void _applyFilters() {
    // Create a new filter model with all the selected filters
    final filter = VehicleFilterModel(
      vehicleType: null, // Will be set in the filter chips
      minDailyRate: _dailyPriceRange.start,
      maxDailyRate: _dailyPriceRange.end,
      minYear: _yearRange.start.toInt(),
      maxYear: _yearRange.end.toInt(),
      make: _selectedMake,
      userLocation: _userPosition,
      maxDistance: _userPosition != null ? _distanceRadius : null,
      availableFrom: _availableFrom,
      availableUntil: _availableUntil,
      requiredFeatures: _selectedFeatures.isNotEmpty ? _selectedFeatures : null,
      searchQuery: _searchController.text.isNotEmpty ? _searchController.text : null,
      sortOrder: _sortOrder,
    );
    
    // Update the filter in the provider
    ref.read(vehicleFilterProvider.notifier).updateFilter(filter);
    
    // Navigate back to the vehicle list screen
    Navigator.pop(context);
  }

  void _resetFilters() {
    setState(() {
      _searchController.clear();
      _selectedMake = null;
      _selectedFeatures = [];
      _availableFrom = null;
      _availableUntil = null;
      _sortOrder = VehicleSortOrder.newest;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    // Watch available makes and features
    final makesAsync = ref.watch(vehicleMakesProvider);
    final featuresAsync = ref.watch(vehicleFeaturesProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Advanced Search'),
        actions: [
          TextButton(
            onPressed: _resetFilters,
            child: const Text('Reset'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by make, model, features...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: theme.colorScheme.surfaceVariant,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Vehicle type filter
            Text('Vehicle Type', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: VehicleType.values.map((type) {
                final filter = ref.watch(vehicleFilterProvider);
                final isSelected = filter.vehicleType == type;
                
                return FilterChip(
                  label: Text(
                    type.toString().split('.').last[0].toUpperCase() +
                        type.toString().split('.').last.substring(1),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    ref.read(vehicleFilterProvider.notifier).setVehicleType(
                      selected ? type : null,
                    );
                  },
                  selectedColor: theme.colorScheme.primaryContainer,
                  checkmarkColor: theme.colorScheme.onPrimaryContainer,
                );
              }).toList(),
            ),
            
            const SizedBox(height: 24),
            
            // Price range filter
            Text('Daily Price Range', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            RangeSlider(
              values: _dailyPriceRange,
              min: 500,
              max: 10000,
              divisions: 19,
              labels: RangeLabels(
                'Rs.${_dailyPriceRange.start.toInt()}',
                'Rs.${_dailyPriceRange.end.toInt()}',
              ),
              onChanged: (values) {
                setState(() {
                  // Update the price range
                  // _dailyPriceRange = values;
                });
              },
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Rs.${_dailyPriceRange.start.toInt()}'),
                  Text('Rs.${_dailyPriceRange.end.toInt()}'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Year range filter
            Text('Year Range', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            RangeSlider(
              values: _yearRange,
              min: DateTime.now().year - 20,
              max: DateTime.now().year.toDouble(),
              divisions: 20,
              labels: RangeLabels(
                '${_yearRange.start.toInt()}',
                '${_yearRange.end.toInt()}',
              ),
              onChanged: (values) {
                setState(() {
                  // _yearRange = values;
                });
              },
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('${_yearRange.start.toInt()}'),
                  Text('${_yearRange.end.toInt()}'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Make filter
            Text('Vehicle Make', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            makesAsync.when(
              data: (makes) {
                return DropdownButtonFormField<String>(
                  value: _selectedMake,
                  decoration: const InputDecoration(
                    hintText: 'Select make',
                    border: OutlineInputBorder(),
                  ),
                  items: makes.map((make) {
                    return DropdownMenuItem<String>(
                      value: make,
                      child: Text(make),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedMake = value;
                    });
                  },
                );
              },
              loading: () => const AppLoadingIndicator(),
              error: (error, stack) => AppErrorWidget(
                message: 'Failed to load vehicle makes: $error',
                onRetry: () => ref.refresh(vehicleMakesProvider),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Distance filter
            Text('Distance', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Slider(
                    value: _distanceRadius,
                    min: 1,
                    max: 50,
                    divisions: 49,
                    label: '${_distanceRadius.toInt()} km',
                    onChanged: _userPosition == null
                        ? null
                        : (value) {
                            setState(() {
                              _distanceRadius = value;
                            });
                          },
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _isLoadingLocation
                      ? null
                      : _getCurrentLocation,
                  icon: _isLoadingLocation
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.my_location, size: 16),
                  label: const Text('Use My Location'),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('1 km'),
                  Text('${_distanceRadius.toInt()} km'),
                  const Text('50 km'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Availability dates
            Text('Availability Dates', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'From',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    readOnly: true,
                    controller: TextEditingController(
                      text: _availableFrom != null
                          ? DateFormat('MMM d, yyyy').format(_availableFrom!)
                          : '',
                    ),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _availableFrom ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState(() {
                          _availableFrom = date;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'To',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    readOnly: true,
                    controller: TextEditingController(
                      text: _availableUntil != null
                          ? DateFormat('MMM d, yyyy').format(_availableUntil!)
                          : '',
                    ),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _availableUntil ?? (_availableFrom != null ? _availableFrom!.add(const Duration(days: 1)) : DateTime.now().add(const Duration(days: 1))),
                        firstDate: _availableFrom ?? DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState(() {
                          _availableUntil = date;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Features filter
            Text('Vehicle Features', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            featuresAsync.when(
              data: (features) {
                return Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: features.map((feature) {
                    final isSelected = _selectedFeatures.contains(feature);
                    return FilterChip(
                      label: Text(feature),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedFeatures.add(feature);
                          } else {
                            _selectedFeatures.remove(feature);
                          }
                        });
                      },
                      selectedColor: theme.colorScheme.primaryContainer,
                      checkmarkColor: theme.colorScheme.onPrimaryContainer,
                    );
                  }).toList(),
                );
              },
              loading: () => const AppLoadingIndicator(),
              error: (error, stack) => AppErrorWidget(
                message: 'Failed to load vehicle features: $error',
                onRetry: () => ref.refresh(vehicleFeaturesProvider),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Sort order
            Text('Sort By', style: textTheme.titleMedium),
            const SizedBox(height: 8),
            DropdownButtonFormField<VehicleSortOrder>(
              value: _sortOrder,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              items: VehicleSortOrder.values.map((order) {
                String label;
                switch (order) {
                  case VehicleSortOrder.newest:
                    label = 'Newest First';
                    break;
                  case VehicleSortOrder.oldest:
                    label = 'Oldest First';
                    break;
                  case VehicleSortOrder.priceAsc:
                    label = 'Price: Low to High';
                    break;
                  case VehicleSortOrder.priceDesc:
                    label = 'Price: High to Low';
                    break;
                  case VehicleSortOrder.nearest:
                    label = 'Nearest First';
                    break;
                  case VehicleSortOrder.highestRated:
                    label = 'Highest Rated';
                    break;
                  case VehicleSortOrder.mostPopular:
                    label = 'Most Popular';
                    break;
                }
                return DropdownMenuItem<VehicleSortOrder>(
                  value: order,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortOrder = value;
                  });
                }
              },
            ),
            
            const SizedBox(height: 32),
            
            // Apply filters button
            SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: _applyFilters,
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12.0),
                  child: Text(
                    'Apply Filters',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
