import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

import 'package:gaadi_sewa/features/vehicles/presentation/providers/vehicle_provider.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/screens/vehicle_detail_screen.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/screens/advanced_search_screen.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_card.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/filter_badge.dart';
import 'package:gaadi_sewa/core/widgets/error_view.dart';
import 'package:gaadi_sewa/core/widgets/loading_view.dart';
import 'package:go_router/go_router.dart';

class VehicleListScreen extends ConsumerStatefulWidget {
  final VehicleType? initialVehicleType;

  const VehicleListScreen({
    Key? key,
    this.initialVehicleType,
  }) : super(key: key);

  @override
  ConsumerState<VehicleListScreen> createState() => _VehicleListScreenState();
}

class _VehicleListScreenState extends ConsumerState<VehicleListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isLoadingLocation = false;
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();

    // Set initial vehicle type if provided
    if (widget.initialVehicleType != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(vehicleFilterProvider.notifier)
            .setVehicleType(widget.initialVehicleType);
      });
    }

    // Initialize search controller
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      setState(() {
        _searchQuery = query;
        _isSearching = true;
      });
    } else {
      setState(() {
        _searchQuery = '';
        _isSearching = false;
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLoadingLocation = true);
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Update the filter with the user's location
      ref.read(vehicleFilterProvider.notifier).setLocation(
            location: position,
            maxDistance: 10.0, // Default 10 km radius
          );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not get current location')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingLocation = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filter = ref.watch(vehicleFilterProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Available Vehicles'),
        actions: [
          IconButton(
            icon: const Icon(Icons.tune),
            onPressed: () =>
                Navigator.pushNamed(context, AdvancedSearchScreen.routeName),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search vehicles...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _isSearching = false;
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: theme.colorScheme.surfaceVariant,
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
              ),
            ),
          ),

          // Active Filters
          if (filter.hasActiveFilters)
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Active Filters (${filter.activeFilterCount})',
                        style: theme.textTheme.titleSmall,
                      ),
                      TextButton(
                        onPressed: () {
                          ref
                              .read(vehicleFilterProvider.notifier)
                              .resetFilter();
                        },
                        child: const Text('Clear All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      if (filter.vehicleType != null)
                        FilterBadge(
                          label:
                              '${filter.vehicleType.toString().split('.').last[0].toUpperCase()}${filter.vehicleType.toString().split('.').last.substring(1)}',
                          onDelete: () => ref
                              .read(vehicleFilterProvider.notifier)
                              .setVehicleType(null),
                        ),
                      if (filter.minDailyRate != null ||
                          filter.maxDailyRate != null)
                        FilterBadge(
                          label: filter.minDailyRate != null &&
                                  filter.maxDailyRate != null
                              ? 'Rs.${filter.minDailyRate!.toInt()} - ${filter.maxDailyRate!.toInt()}/day'
                              : filter.minDailyRate != null
                                  ? 'Min Rs.${filter.minDailyRate!.toInt()}/day'
                                  : 'Max Rs.${filter.maxDailyRate!.toInt()}/day',
                          onDelete: () => ref
                              .read(vehicleFilterProvider.notifier)
                              .setPriceRange(),
                        ),
                      if (filter.make != null)
                        FilterBadge(
                          label: filter.make!,
                          onDelete: () => ref
                              .read(vehicleFilterProvider.notifier)
                              .setMakeModel(make: null),
                        ),
                      if (filter.model != null)
                        FilterBadge(
                          label: filter.model!,
                          onDelete: () => ref
                              .read(vehicleFilterProvider.notifier)
                              .setMakeModel(model: null),
                        ),
                      if (filter.minYear != null || filter.maxYear != null)
                        FilterBadge(
                          label:
                              filter.minYear != null && filter.maxYear != null
                                  ? '${filter.minYear} - ${filter.maxYear}'
                                  : filter.minYear != null
                                      ? 'From ${filter.minYear}'
                                      : 'Until ${filter.maxYear}',
                          onDelete: () => ref
                              .read(vehicleFilterProvider.notifier)
                              .setYearRange(),
                        ),
                      if (filter.userLocation != null &&
                          filter.maxDistance != null)
                        FilterBadge(
                          label: 'Within ${filter.maxDistance!.toInt()} km',
                          onDelete: () => ref
                              .read(vehicleFilterProvider.notifier)
                              .setLocation(),
                        ),
                      if (filter.requiredFeatures != null &&
                          filter.requiredFeatures!.isNotEmpty)
                        ...filter.requiredFeatures!.map(
                          (feature) => FilterBadge(
                            label: feature,
                            onDelete: () {
                              final updatedFeatures =
                                  List<String>.from(filter.requiredFeatures!)
                                    ..remove(feature);
                              ref
                                  .read(vehicleFilterProvider.notifier)
                                  .setFeatures(
                                    updatedFeatures.isEmpty
                                        ? null
                                        : updatedFeatures,
                                  );
                            },
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

          // Search results heading
          if (_isSearching)
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Text(
                'Search results for "$_searchQuery"',
                style: theme.textTheme.titleMedium,
              ),
            ),

          // Vehicle List
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                // Use search provider if searching, otherwise use advanced filter provider
                final vehiclesAsync = _isSearching
                    ? ref.watch(vehicleSearchProvider(_searchQuery))
                    : ref.watch(vehiclesAdvancedProvider(filter));

                return vehiclesAsync.when(
                  data: (vehicles) {
                    if (vehicles.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.directions_car_outlined,
                              size: 64,
                              color: theme.colorScheme.primary.withOpacity(0.5),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No vehicles found',
                              style: theme.textTheme.titleLarge,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _isSearching
                                  ? 'Try a different search term'
                                  : 'Try adjusting your filters',
                              style: theme.textTheme.bodyMedium,
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            if (_isSearching)
                              ElevatedButton(
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _isSearching = false;
                                    _searchQuery = '';
                                  });
                                },
                                child: const Text('Clear Search'),
                              )
                            else
                              ElevatedButton(
                                onPressed: () {
                                  ref
                                      .read(vehicleFilterProvider.notifier)
                                      .resetFilter();
                                },
                                child: const Text('Clear Filters'),
                              ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () => _isSearching
                          ? ref.refresh(
                              vehicleSearchProvider(_searchQuery).future)
                          : ref
                              .refresh(vehiclesAdvancedProvider(filter).future),
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        itemCount: vehicles.length,
                        itemBuilder: (context, index) {
                          final vehicle = vehicles[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: VehicleCard(
                              vehicle: vehicle,
                              onTap: () {
                                context.pushNamed(
                                  VehicleDetailScreen.routeName,
                                  extra: vehicle,
                                );
                              },
                            ),
                          );
                        },
                      ),
                    );
                  },
                  loading: () => const LoadingView(),
                  error: (error, stack) => ErrorView(
                    error: error.toString(),
                    onRetry: () => _isSearching
                        ? ref.refresh(vehicleSearchProvider(_searchQuery))
                        : ref.refresh(vehiclesAdvancedProvider(filter)),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isLoadingLocation
            ? null
            : () {
                // Refresh with current location
                _getCurrentLocation();
              },
        label: _isLoadingLocation
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text('Near Me'),
        icon: const Icon(Icons.my_location),
      ),
    );
  }
}
