import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/review_summary_widget.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:cached_network_image/cached_network_image.dart';

class VehicleDetailScreen extends ConsumerWidget {
  static const String routeName = 'vehicle-detail';
  final VehicleModel? vehicle;
  final String? vehicleId;

  const VehicleDetailScreen({
    Key? key,
    this.vehicle,
    this.vehicleId,
  })  : assert(vehicle != null || vehicleId != null,
            'Either vehicle or vehicleId must be provided'),
        super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    // If vehicle is not provided, we need to load it by ID
    if (vehicle == null && vehicleId != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Vehicle Details')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading vehicle details...'),
            ],
          ),
        ),
      );
    }

    final vehicleData = vehicle!;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with Image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: vehicleData.images?.isNotEmpty == true
                  ? CachedNetworkImage(
                      imageUrl: vehicleData.images!.first,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.directions_car, size: 64),
                      ),
                    )
                  : Container(
                      color: Colors.grey[200],
                      child: const Icon(Icons.directions_car, size: 64),
                    ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: () {
                  // TODO: Implement share functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Share functionality coming soon')),
                  );
                },
              ),
            ],
          ),

          // Vehicle Details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${vehicleData.year} ${vehicleData.make} ${vehicleData.model}',
                              style: textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primaryContainer,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Text(
                                    _formatVehicleType(vehicleData.type),
                                    style: textTheme.labelSmall?.copyWith(
                                      color:
                                          theme.colorScheme.onPrimaryContainer,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                // Add review rating badge here
                                ReviewRatingBadge(
                                  vehicleId: vehicleData.id,
                                  backgroundColor: theme.colorScheme.secondary,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Rs. ${vehicleData.dailyRate.toStringAsFixed(0)}',
                            style: textTheme.titleLarge?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'per day',
                            style: textTheme.bodySmall,
                          ),
                          const SizedBox(height: 4),
                          if (vehicleData.hourlyRate != null)
                            Text(
                              'Rs. ${vehicleData.hourlyRate!.toStringAsFixed(0)}/hour',
                              style: textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Availability Status
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: vehicleData.isAvailable
                          ? Colors.green.withOpacity(0.1)
                          : Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: vehicleData.isAvailable
                                ? Colors.green
                                : Colors.orange,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          vehicleData.isAvailable
                              ? 'Available Now'
                              : 'Currently Unavailable',
                          style: textTheme.bodyMedium?.copyWith(
                            color: vehicleData.isAvailable
                                ? Colors.green
                                : Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Description
                  if (vehicleData.description != null) ...[
                    Text(
                      'Description',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      vehicleData.description!,
                      style: textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Features
                  if (vehicleData.features?.isNotEmpty == true) ...[
                    Text(
                      'Features',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: vehicleData.features!
                          .map((feature) => Chip(
                                label: Text(feature),
                                backgroundColor:
                                    theme.colorScheme.surfaceVariant,
                              ))
                          .toList(),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Location
                  Text(
                    'Location',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // TODO: Add map widget here
                  Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.map, size: 48, color: Colors.grey),
                          const SizedBox(height: 8),
                          Text(
                            'Map view coming soon',
                            style: textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Reviews Section
                  ReviewSummaryWidget(vehicle: vehicleData),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton(
            onPressed: vehicleData.isAvailable
                ? () {
                    // TODO: Implement booking functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Booking functionality coming soon')),
                    );
                  }
                : null,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              vehicleData.isAvailable ? 'Book Now' : 'Currently Unavailable',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _formatVehicleType(VehicleType type) {
    return type.toString().split('.').last[0].toUpperCase() +
        type.toString().split('.').last.substring(1);
  }
}
