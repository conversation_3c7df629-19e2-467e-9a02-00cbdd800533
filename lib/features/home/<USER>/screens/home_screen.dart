import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/features/auth/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

class _VehicleCategory {
  final VehicleType? type;
  final String title;
  final IconData icon;
  final Color color;

  const _VehicleCategory({
    required this.type,
    required this.title,
    required this.icon,
    required this.color,
  });
}

class _CategoryCard extends StatelessWidget {
  final _VehicleCategory category;
  final VoidCallback onTap;

  const _CategoryCard({
    Key? key,
    required this.category,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              category.icon,
              size: 32,
              color: category.color,
            ),
            const SizedBox(height: 8),
            Text(
              category.title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authProvider);
    final authNotifier = ref.read(authProvider.notifier);
    final theme = Theme.of(context);

    // Vehicle categories
    final categories = [
      _VehicleCategory(
        type: VehicleType.bicycle,
        title: 'Bicycles',
        icon: Icons.pedal_bike,
        color: AppColors.primary,
      ),
      _VehicleCategory(
        type: VehicleType.scooter,
        title: 'Scooters',
        icon: Icons.electric_scooter,
        color: Colors.green,
      ),
      _VehicleCategory(
        type: VehicleType.car,
        title: 'Cars',
        icon: Icons.directions_car,
        color: Colors.blue,
      ),
      _VehicleCategory(
        type: null,
        title: 'All Vehicles',
        icon: Icons.view_list,
        color: Colors.purple,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('GaadiSewa+'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await authNotifier.signOut();
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // TODO: Refresh user data if needed
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome${user?.userMetadata?['full_name'] != null ? ' ${user!.userMetadata!['full_name']}' : ''}',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Find your perfect ride for any occasion',
                      style: theme.textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Categories grid
              Text(
                'Categories',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.2,
                ),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  return _CategoryCard(
                    category: category,
                    onTap: () {
                      // Navigate to vehicle list with the selected category filter
                      context.push(
                        '/vehicles',
                        extra: category.type,
                      );
                    },
                  );
                },
              ),
              const SizedBox(height: 24),
              // Popular vehicles section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Popular Vehicles',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      context.push('/vehicles');
                    },
                    child: const Text('View All'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(
                  child: Text(
                    'Popular vehicles will appear here',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // TODO: Show vehicle location on map
        },
        icon: const Icon(Icons.map),
        label: const Text('View on Map'),
      ),
    );
  }
}
