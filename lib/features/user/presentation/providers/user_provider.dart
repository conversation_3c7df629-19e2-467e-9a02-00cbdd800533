import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/user/domain/models/user_profile_model.dart';
import 'package:gaadi_sewa/features/user/presentation/providers/auth_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final userProfileProvider = FutureProvider.family<UserProfileModel?, String>((ref, userId) async {
  if (userId.isEmpty) return null;
  
  try {
    final client = ref.watch(supabaseClientProvider);
    final response = await client
        .from('profiles')
        .select()
        .eq('id', userId)
        .single();
    
    return UserProfileModel.fromJson(response);
  } on PostgrestException catch (e) {
    if (e.code == 'PGRST116') {
      // No rows returned, user profile not found
      return null;
    }
    throw ServerFailure(message: e.message);
  } catch (e) {
    throw ServerFailure(message: 'Failed to fetch user profile: $e');
  }
});

final currentUserProfileProvider = FutureProvider<UserProfileModel?>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return null;
  
  return ref.watch(userProfileProvider(currentUser.id).future);
});
