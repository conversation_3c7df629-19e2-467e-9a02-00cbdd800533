import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/user/domain/models/user_profile_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

final authStateProvider = StreamProvider<AuthState>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return client.auth.onAuthStateChange;
});

final currentUserProvider = Provider<UserProfileModel?>((ref) {
  final authState = ref.watch(authStateProvider);
  
  return authState.when(
    data: (state) {
      final user = state.session?.user;
      if (user == null) return null;
      
      return UserProfileModel(
        id: user.id,
        email: user.email,
        fullName: user.userMetadata?['full_name'] as String?,
        phoneNumber: user.phone,
      );
    },
    loading: () => null,
    error: (_, __) => null,
  );
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  
  return authState.when(
    data: (state) => state.session != null,
    loading: () => false,
    error: (_, __) => false,
  );
});
