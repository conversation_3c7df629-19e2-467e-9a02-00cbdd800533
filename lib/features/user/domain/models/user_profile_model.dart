import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_profile_model.g.dart';

@JsonSerializable()
class UserProfileModel extends Equatable {
  final String id;
  final String? fullName;
  final String? email;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String? address;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? preferences;

  const UserProfileModel({
    required this.id,
    this.fullName,
    this.email,
    this.phoneNumber,
    this.profileImageUrl,
    this.address,
    this.createdAt,
    this.updatedAt,
    this.preferences,
  });

  factory UserProfileModel.fromJson(Map<String, dynamic> json) => 
      _$UserProfileModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileModelToJson(this);

  UserProfileModel copyWith({
    String? id,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? profileImageUrl,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? preferences,
  }) {
    return UserProfileModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  List<Object?> get props => [
    id,
    fullName,
    email,
    phoneNumber,
    profileImageUrl,
    address,
    createdAt,
    updatedAt,
    preferences,
  ];
}
