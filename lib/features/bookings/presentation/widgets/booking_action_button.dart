import 'package:flutter/material.dart';

class BookingActionButton extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;
  final Color backgroundColor;
  final Color textColor;
  final bool isOutlined;
  final Color borderColor;
  final double height;
  final double? width;
  final bool isLoading;
  final double borderRadius;
  final double elevation;
  final EdgeInsetsGeometry? padding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double fontSize;
  final FontWeight fontWeight;

  const BookingActionButton({
    Key? key,
    required this.label,
    required this.onPressed,
    this.backgroundColor = Colors.blue,
    this.textColor = Colors.white,
    this.isOutlined = false,
    this.borderColor = Colors.blue,
    this.height = 48,
    this.width,
    this.isLoading = false,
    this.borderRadius = 8,
    this.elevation = 0,
    this.padding,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
  }) : super(key: key);

  const BookingActionButton.outlined({
    Key? key,
    required String label,
    required VoidCallback onPressed,
    Color borderColor = Colors.blue,
    Color textColor = Colors.blue,
    double height = 48,
    double? width,
    bool isLoading = false,
    double borderRadius = 8,
    double elevation = 0,
    EdgeInsetsGeometry? padding,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w600,
  }) : this(
          key: key,
          label: label,
          onPressed: onPressed,
          backgroundColor: Colors.transparent,
          textColor: textColor,
          isOutlined: true,
          borderColor: borderColor,
          height: height,
          width: width,
          isLoading: isLoading,
          borderRadius: borderRadius,
          elevation: elevation,
          padding: padding,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          fontSize: fontSize,
          fontWeight: fontWeight,
        );

  @override
  Widget build(BuildContext context) {
    final buttonChild = isLoading
        ? SizedBox(
            height: height * 0.6,
            width: height * 0.6,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isOutlined ? textColor : Colors.white,
              ),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (prefixIcon != null) ...[
                prefixIcon!,
                const SizedBox(width: 8),
              ],
              Text(
                label,
                style: TextStyle(
                  color: textColor,
                  fontSize: fontSize,
                  fontWeight: fontWeight,
                ),
              ),
              if (suffixIcon != null) ...[
                const SizedBox(width: 8),
                suffixIcon!,
              ],
            ],
          );

    return SizedBox(
      width: width,
      height: height,
      child: isOutlined
          ? OutlinedButton(
              onPressed: isLoading ? null : onPressed,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: borderColor),
                backgroundColor: backgroundColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(borderRadius),
                ),
                elevation: elevation,
                padding: padding,
              ),
              child: buttonChild,
            )
          : ElevatedButton(
              onPressed: isLoading ? null : onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: backgroundColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(borderRadius),
                ),
                elevation: elevation,
                padding: padding,
              ),
              child: buttonChild,
            ),
    );
  }
}
