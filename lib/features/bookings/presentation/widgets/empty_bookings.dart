import 'package:flutter/material.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';

class EmptyBookings extends StatelessWidget {
  final bool isOwnerView;
  final String filter;
  
  const EmptyBookings({
    Key? key,
    this.isOwnerView = false,
    this.filter = 'all',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final icon = isOwnerView ? Icons.directions_car_filled : Icons.calendar_today;
    String title;
    String message;
    
    if (filter == 'upcoming') {
      title = 'No Upcoming Bookings';
      message = isOwnerView
          ? 'You don\'t have any upcoming rentals.'
          : 'You don\'t have any upcoming bookings. Browse vehicles to book one!';
    } else if (filter == 'past') {
      title = 'No Past Bookings';
      message = isOwnerView
          ? 'Your past rentals will appear here.'
          : 'Your past bookings will appear here.';
    } else if (filter == 'pending') {
      title = 'No Pending Bookings';
      message = isOwnerView
          ? 'You don\'t have any pending rental requests.'
          : 'You don\'t have any pending bookings.';
    } else if (filter == 'confirmed') {
      title = 'No Confirmed Bookings';
      message = isOwnerView
          ? 'You don\'t have any confirmed rentals.'
          : 'You don\'t have any confirmed bookings.';
    } else if (filter == 'cancelled') {
      title = 'No Cancelled Bookings';
      message = isOwnerView
          ? 'Your cancelled rentals will appear here.'
          : 'Your cancelled bookings will appear here.';
    } else {
      title = isOwnerView ? 'No Rentals Yet' : 'No Bookings Yet';
      message = isOwnerView
          ? 'Your rental history will appear here when you receive bookings.'
          : 'Your booking history will appear here. Start by browsing vehicles!';
    }
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            if (!isOwnerView && filter != 'upcoming')
              ElevatedButton(
                onPressed: () {
                  // Navigate to home screen to browse vehicles
                  Navigator.popUntil(
                    context,
                    (route) => route.isFirst,
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Browse Vehicles',
                  style: TextStyle(fontSize: 16),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
