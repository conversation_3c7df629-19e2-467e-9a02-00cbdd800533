import 'package:flutter/material.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_status_chip.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_image_slider.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_info_row.dart';
import 'package:intl/intl.dart';

class BookingCard extends StatelessWidget {
  final BookingModel booking;
  final bool isOwnerView;
  final Function(String, {String? reason})? onStatusUpdate;

  const BookingCard({
    Key? key,
    required this.booking,
    this.isOwnerView = false,
    this.onStatusUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM d, y');
    final timeFormat = DateFormat('h:mm a');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Vehicle image and status
          Stack(
            children: [
              // Vehicle image
              SizedBox(
                height: 160,
                width: double.infinity,
                child: ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(12)),
                  child: VehicleImageSlider(
                    images: booking.vehicle.images ?? [],
                    height: 160,
                    showIndicators: false,
                  ),
                ),
              ),

              // Status chip
              Positioned(
                top: 12,
                right: 12,
                child: BookingStatusChip(status: booking.status),
              ),
            ],
          ),

          // Booking details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Vehicle title
                Text(
                  '${booking.vehicle.make} ${booking.vehicle.model} (${booking.vehicle.year})',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 8),

                // Booking dates
                _buildInfoRow(
                  icon: Icons.date_range,
                  label:
                      '${dateFormat.format(booking.startDate)} - ${dateFormat.format(booking.endDate)}',
                ),

                const SizedBox(height: 8),

                // Pickup time
                _buildInfoRow(
                  icon: Icons.access_time,
                  label: 'Pickup: ${timeFormat.format(booking.startDate)}',
                ),

                const SizedBox(height: 8),

                // Total price
                _buildInfoRow(
                  icon: Icons.payments,
                  label: 'Total: NPR ${booking.totalAmount.toStringAsFixed(2)}',
                  valueStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),

                const SizedBox(height: 12),

                // Action buttons
                if (onStatusUpdate != null && booking.status.isActive)
                  ..._buildActionButtons(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    TextStyle? valueStyle,
  }) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          label,
          style: valueStyle ?? TextStyle(color: Colors.grey[800]),
        ),
      ],
    );
  }

  List<Widget> _buildActionButtons() {
    final buttons = <Widget>[];

    if (isOwnerView) {
      // Owner actions
      if (booking.status == BookingStatus.pending) {
        buttons.addAll([
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => onStatusUpdate?.call(
                    BookingStatus.rejected.toString().split('.').last,
                    reason: 'Rejected by owner',
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('Reject'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => onStatusUpdate?.call(
                    BookingStatus.confirmed.toString().split('.').last,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('Confirm'),
                ),
              ),
            ],
          ),
        ]);
      }

      if (booking.status == BookingStatus.confirmed) {
        buttons.addAll([
          ElevatedButton(
            onPressed: () => onStatusUpdate?.call(
              BookingStatus.completed.toString().split('.').last,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              minimumSize: const Size(double.infinity, 48),
            ),
            child: const Text('Mark as Completed'),
          ),
        ]);
      }
    } else {
      // Renter actions
      if (booking.status == BookingStatus.pending ||
          booking.status == BookingStatus.confirmed) {
        buttons.addAll([
          OutlinedButton(
            onPressed: () => onStatusUpdate?.call(
              BookingStatus.cancelled.toString().split('.').last,
              reason: 'Cancelled by user',
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              minimumSize: const Size(double.infinity, 48),
            ),
            child: const Text('Cancel Booking'),
          ),
        ]);
      }
    }

    // Add view details button
    buttons.addAll([
      const SizedBox(height: 8),
      TextButton(
        onPressed: () {
          // Navigate to booking details
          // This will be handled by the parent widget
        },
        child: const Text('View Details'),
      ),
    ]);

    return buttons;
  }
}
