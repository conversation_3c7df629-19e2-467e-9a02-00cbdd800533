import 'package:flutter/material.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';

class BookingStatusChip extends StatelessWidget {
  final BookingStatus status;
  final bool small;
  
  const BookingStatusChip({
    Key? key,
    required this.status,
    this.small = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final (color, backgroundColor) = _getStatusColors(status);
    
    return Container(
      padding: small
          ? const EdgeInsets.symmetric(horizontal: 6, vertical: 2)
          : const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status.displayText,
        style: TextStyle(
          color: color,
          fontSize: small ? 10 : 12,
          fontWeight: small ? FontWeight.w500 : FontWeight.w600,
        ),
      ),
    );
  }
  
  (Color, Color) _getStatusColors(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return (const Color(0xFFF59E0B), const Color(0xFFFFFBEB));
      case BookingStatus.confirmed:
        return (const Color(0xFF10B981), const Color(0xFFECFDF5));
      case BookingStatus.cancelled:
        return (const Color(0xFFEF4444), const Color(0xFFFEF2F2));
      case BookingStatus.completed:
        return (const Color(0xFF3B82F6), const Color(0xFFEFF6FF));
      case BookingStatus.rejected:
        return (const Color(0xFF6B7280), const Color(0xFFF9FAFB));
    }
  }
}
