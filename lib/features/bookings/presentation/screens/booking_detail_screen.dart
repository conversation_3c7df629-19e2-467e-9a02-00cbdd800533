import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/presentation/providers/booking_provider.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_action_button.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_info_tile.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_status_chip.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_card.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_image_slider.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_info_row.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_owner_info.dart';
import 'package:intl/intl.dart';

class BookingDetailScreen extends ConsumerStatefulWidget {
  final String bookingId;
  final bool isOwnerView;

  const BookingDetailScreen({
    Key? key,
    required this.bookingId,
    this.isOwnerView = false,
  }) : super(key: key);

  @override
  ConsumerState<BookingDetailScreen> createState() =>
      _BookingDetailScreenState();
}

class _BookingDetailScreenState extends ConsumerState<BookingDetailScreen> {
  @override
  Widget build(BuildContext context) {
    final bookingAsync = ref.watch(bookingProvider(widget.bookingId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Details'),
        centerTitle: true,
      ),
      body: bookingAsync.when(
        data: (booking) => _buildBookingDetails(booking, ref),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Failed to load booking details',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () =>
                    ref.invalidate(bookingProvider(widget.bookingId)),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingDetails(BookingModel booking, WidgetRef ref) {
    final dateFormat = DateFormat('MMM d, y');
    final timeFormat = DateFormat('h:mm a');

    return SingleChildScrollView(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Booking status banner
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            color: _getStatusColor(booking.status).withOpacity(0.1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Booking ${booking.status.displayText}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: _getStatusColor(booking.status),
                        fontWeight: FontWeight.bold,
                      ),
                ),
                BookingStatusChip(status: booking.status),
              ],
            ),
          ),

          // Vehicle details
          const Padding(
            padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
            child: Text(
              'Vehicle Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Vehicle card
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: VehicleCard(vehicle: booking.vehicle, onTap: () {}),
          ),

          // Booking info
          const Padding(
            padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              'Booking Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                BookingInfoTile(
                  icon: Icons.confirmation_number,
                  label: 'Booking ID',
                  value: booking.id,
                  showDivider: true,
                ),
                BookingInfoTile(
                  icon: Icons.calendar_today,
                  label: 'Booking Date',
                  value: dateFormat.format(booking.createdAt),
                  showDivider: true,
                ),
                BookingInfoTile(
                  icon: Icons.date_range,
                  label: 'Rental Period',
                  value:
                      '${dateFormat.format(booking.startDate)} - ${dateFormat.format(booking.endDate)}',
                  showDivider: true,
                ),
                BookingInfoTile(
                  icon: Icons.access_time,
                  label: 'Pickup Time',
                  value: timeFormat.format(booking.startDate),
                  showDivider: true,
                ),
                BookingInfoTile(
                  icon: Icons.monetization_on,
                  label: 'Total Amount',
                  value: 'NPR ${booking.totalAmount.toStringAsFixed(2)}',
                  valueStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          // Owner/Renter info
          const Padding(
            padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
            child: Text(
              'Contact Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: VehicleOwnerInfo(
              ownerId: booking.vehicle.ownerId,
            ),
          ),

          // Action buttons
          if (booking.status.isActive) ..._buildActionButtons(booking, ref),
        ],
      ),
    );
  }

  List<Widget> _buildActionButtons(BookingModel booking, WidgetRef ref) {
    final buttons = <Widget>[];

    if (widget.isOwnerView) {
      // Owner actions
      if (booking.status == BookingStatus.pending) {
        buttons.addAll([
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: BookingActionButton(
                    label: 'Reject',
                    backgroundColor: Colors.red,
                    textColor: Colors.white,
                    onPressed: () => _showRejectDialog(booking.id),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: BookingActionButton(
                    label: 'Confirm',
                    backgroundColor: AppColors.primary,
                    textColor: Colors.white,
                    onPressed: () => _updateBookingStatus(
                      booking.id,
                      BookingStatus.confirmed.toString().split('.').last,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ]);
      }

      if (booking.status == BookingStatus.confirmed) {
        buttons.addAll([
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: BookingActionButton(
              label: 'Mark as Completed',
              backgroundColor: AppColors.primary,
              textColor: Colors.white,
              onPressed: () => _updateBookingStatus(
                booking.id,
                BookingStatus.completed.toString().split('.').last,
              ),
            ),
          ),
        ]);
      }
    } else {
      // Renter actions
      if (booking.status == BookingStatus.pending ||
          booking.status == BookingStatus.confirmed) {
        buttons.addAll([
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: BookingActionButton(
              label: 'Cancel Booking',
              backgroundColor: Colors.red,
              textColor: Colors.white,
              onPressed: () => _showCancelDialog(booking.id),
            ),
          ),
        ]);
      }

      // Add contact owner button
      buttons.addAll([
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: BookingActionButton.outlined(
            label: 'Contact Owner',
            borderColor: AppColors.primary,
            textColor: AppColors.primary,
            onPressed: () {
              // TODO: Implement contact owner functionality
            },
          ),
        ),
      ]);
    }

    return buttons;
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return Colors.green;
      case BookingStatus.cancelled:
        return Colors.red;
      case BookingStatus.completed:
        return Colors.blue;
      case BookingStatus.rejected:
        return Colors.red;
    }
  }

  void _showCancelDialog(String bookingId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: const Text(
            'Are you sure you want to cancel this booking? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No, Keep It'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _updateBookingStatus(
                bookingId,
                BookingStatus.cancelled.toString().split('.').last,
                reason: 'Cancelled by user',
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _showRejectDialog(String bookingId) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Please provide a reason for rejecting this booking:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                hintText: 'Enter reason...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final reason = reasonController.text.trim();
              if (reason.isNotEmpty) {
                Navigator.pop(context);
                _updateBookingStatus(
                  bookingId,
                  BookingStatus.rejected.toString().split('.').last,
                  reason: reason,
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateBookingStatus(
    String bookingId,
    String status, {
    String? reason,
  }) async {
    final notifier = ref.read(bookingNotifierProvider.notifier);

    try {
      await notifier.updateBookingStatus(
        bookingId,
        status,
        rejectionReason: reason,
      );

      // Refresh the booking details
      ref.invalidate(bookingProvider(widget.bookingId));

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Booking ${status.toLowerCase()} successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update booking: $e')),
        );
      }
    }
  }
}
