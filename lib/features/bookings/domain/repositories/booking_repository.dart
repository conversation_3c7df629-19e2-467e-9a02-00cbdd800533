import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';

abstract class BookingRepository {
  // Create a new booking
  Future<Either<Failure, BookingModel>> createBooking(BookingModel booking);
  
  // Get a booking by ID
  Future<Either<Failure, BookingModel>> getBooking(String id);
  
  // Get all bookings for a user
  Future<Either<Failure, List<BookingModel>>> getUserBookings(String userId);
  
  // Get all bookings for a vehicle owner
  Future<Either<Failure, List<BookingModel>>> getOwnerBookings(String ownerId);
  
  // Update booking status
  Future<Either<Failure, void>> updateBookingStatus(
    String bookingId, 
    String status, 
    {String? rejectionReason}
  );
  
  // Cancel a booking
  Future<Either<Failure, void>> cancelBooking(String bookingId, String reason);
  
  // Check vehicle availability
  Future<Either<Failure, bool>> checkAvailability(
    String vehicleId, 
    DateTime startDate, 
    DateTime endDate, 
    {String? excludeBookingId}
  );
  
  // Calculate booking price
  Future<Either<Failure, double>> calculatePrice(
    String vehicleId, 
    DateTime startDate, 
    DateTime endDate
  );
}
