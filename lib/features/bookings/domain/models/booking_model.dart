import 'package:equatable/equatable.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

enum BookingStatus {
  pending,
  confirmed,
  cancelled,
  completed,
  rejected,
}

class BookingModel extends Equatable {
  final String id;
  final String userId;
  final String vehicleId;
  final VehicleModel vehicle;
  final DateTime startDate;
  final DateTime endDate;
  final double totalAmount;
  final BookingStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? paymentIntentId;
  final String? rejectionReason;

  const BookingModel({
    required this.id,
    required this.userId,
    required this.vehicleId,
    required this.vehicle,
    required this.startDate,
    required this.endDate,
    required this.totalAmount,
    this.status = BookingStatus.pending,
    required this.createdAt,
    this.updatedAt,
    this.paymentIntentId,
    this.rejectionReason,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        vehicleId,
        vehicle,
        startDate,
        endDate,
        totalAmount,
        status,
        createdAt,
        updatedAt,
        paymentIntentId,
        rejectionReason,
      ];

  BookingModel copyWith({
    String? id,
    String? userId,
    String? vehicleId,
    VehicleModel? vehicle,
    DateTime? startDate,
    DateTime? endDate,
    double? totalAmount,
    BookingStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? paymentIntentId,
    String? rejectionReason,
  }) {
    return BookingModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      vehicleId: vehicleId ?? this.vehicleId,
      vehicle: vehicle ?? this.vehicle,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'vehicle_id': vehicleId,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_amount': totalAmount,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'payment_intent_id': paymentIntentId,
      'rejection_reason': rejectionReason,
    };
  }

  factory BookingModel.fromJson(Map<String, dynamic> json, VehicleModel vehicle) {
    return BookingModel(
      id: json['id'],
      userId: json['user_id'],
      vehicleId: json['vehicle_id'],
      vehicle: vehicle,
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      totalAmount: (json['total_amount'] as num).toDouble(),
      status: BookingStatus.values.firstWhere(
        (e) => e.toString() == 'BookingStatus.${json['status']}',
        orElse: () => BookingStatus.pending,
      ),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      paymentIntentId: json['payment_intent_id'],
      rejectionReason: json['rejection_reason'],
    );
  }
}

// Extension to handle booking status display text
extension BookingStatusExtension on BookingStatus {
  String get displayText {
    switch (this) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.rejected:
        return 'Rejected';
    }
  }

  bool get isActive {
    return this == BookingStatus.pending || this == BookingStatus.confirmed;
  }
}
