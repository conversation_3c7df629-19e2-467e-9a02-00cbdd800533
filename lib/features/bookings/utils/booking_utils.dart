import 'package:flutter/material.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:intl/intl.dart';

class BookingUtils {
  static final DateFormat _dateFormat = DateFormat('MMM d, yyyy');
  static final DateFormat _timeFormat = DateFormat('h:mm a');
  static final DateFormat _dateTimeFormat = DateFormat('MMM d, yyyy h:mm a');
  static final DateFormat _apiDateFormat = DateFormat('yyyy-MM-dd');

  /// Formats a DateTime to a display date string (e.g., "Jan 1, 2023")
  static String formatDate(DateTime date) {
    return _dateFormat.format(date);
  }

  /// Formats a DateTime to a time string (e.g., "2:30 PM")
  static String formatTime(DateTime time) {
    return _timeFormat.format(time);
  }

  /// Formats a DateTime to a date and time string (e.g., "Jan 1, 2023 2:30 PM")
  static String formatDateTime(DateTime dateTime) {
    return _dateTimeFormat.format(dateTime);
  }

  /// Formats a DateTime to an API date string (e.g., "2023-01-01")
  static String formatDateForApi(DateTime date) {
    return _apiDateFormat.format(date);
  }

  /// Parses an API date string to a DateTime
  static DateTime? parseDateFromApi(String? dateString) {
    if (dateString == null) return null;
    try {
      return _apiDateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Calculates the number of days between two dates
  static int calculateDays(DateTime start, DateTime end) {
    // Add one day to include both start and end dates
    return end.difference(start).inDays + 1;
  }

  /// Calculates the total price for a booking
  static double calculateTotalPrice({
    required double dailyRate,
    required DateTime startDate,
    required DateTime endDate,
    double? discount,
    double? taxRate,
  }) {
    final days = calculateDays(startDate, endDate);
    var subtotal = dailyRate * days;
    
    // Apply discount if any
    if (discount != null && discount > 0) {
      subtotal -= (subtotal * discount / 100);
    }
    
    // Apply tax if any
    if (taxRate != null && taxRate > 0) {
      subtotal += (subtotal * taxRate / 100);
    }
    
    return subtotal;
  }

  /// Gets the display text for a booking status
  static String getStatusText(BookingStatus status) {
    return status.toString().split('.').last;
  }

  /// Gets the color for a booking status
  static Color getStatusColor(BookingStatus status, BuildContext context) {
    final theme = Theme.of(context);
    
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return theme.primaryColor;
      case BookingStatus.completed:
        return Colors.green;
      case BookingStatus.cancelled:
        return Colors.red;
      case BookingStatus.rejected:
        return Colors.grey;
    }
  }

  /// Gets the icon for a booking status
  static IconData getStatusIcon(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Icons.pending_actions;
      case BookingStatus.confirmed:
        return Icons.check_circle;
      case BookingStatus.completed:
        return Icons.done_all;
      case BookingStatus.cancelled:
        return Icons.cancel;
      case BookingStatus.rejected:
        return Icons.block;
    }
  }

  /// Validates if the selected dates are valid for booking
  static String? validateBookingDates({
    required DateTime startDate,
    required DateTime endDate,
    DateTime? minDate,
    DateTime? maxDate,
    List<DateTimeRange>? blockedDates = const [],
  }) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Check if start date is in the past
    if (startDate.isBefore(today)) {
      return 'Start date cannot be in the past';
    }
    
    // Check if end date is before start date
    if (endDate.isBefore(startDate)) {
      return 'End date must be after start date';
    }
    
    // Check if dates are within allowed range
    if (minDate != null && startDate.isBefore(minDate)) {
      return 'Start date must be after ${formatDate(minDate)}';
    }
    
    if (maxDate != null && endDate.isAfter(maxDate)) {
      return 'End date must be before ${formatDate(maxDate)}';
    }
    
    // Check for blocked dates
    if (blockedDates != null) {
      for (final range in blockedDates) {
        if ((startDate.isAfter(range.start) && startDate.isBefore(range.end)) ||
            (endDate.isAfter(range.start) && endDate.isBefore(range.end)) ||
            (startDate.isBefore(range.start) && endDate.isAfter(range.end))) {
          return 'Selected dates conflict with an existing booking';
        }
      }
    }
    
    return null;
  }

  /// Formats a price with currency
  static String formatPrice(double price, {String currency = 'NPR'}) {
    final formatter = NumberFormat.currency(
      symbol: '$currency ',
      decimalDigits: 0,
    );
    return formatter.format(price);
  }

  /// Gets the duration text (e.g., "2 days", "1 week")
  static String getDurationText(DateTime start, DateTime end) {
    final days = calculateDays(start, end);
    if (days == 1) return '1 day';
    if (days < 7) return '$days days';
    if (days == 7) return '1 week';
    if (days < 30) return '${(days / 7).round()} weeks';
    if (days == 30) return '1 month';
    return '${(days / 30).round()} months';
  }
}
