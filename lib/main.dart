import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'core/config/app_config.dart';
import 'core/routes/app_router.dart';
import 'core/theme/app_theme.dart';
import 'features/auth/presentation/providers/auth_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  await dotenv.load(fileName: ".env");
  
  // Initialize Supabase
  await AppConfig.initialize();
  
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen(
      authProvider,
      (_, next) {
        final currentPath = router.routerDelegate.currentConfiguration.uri.path;
        
        if (next == null) {
          // User signed out
          if (currentPath != '/' && 
              !currentPath.startsWith('/login') && 
              !currentPath.startsWith('/signup')) {
            router.go('/');
          }
        } else {
          // User signed in
          if (currentPath == '/' || 
              currentPath.startsWith('/login') || 
              currentPath.startsWith('/signup')) {
            router.go('/home');
          }
        }
      },
    );

    // Show loading indicator while checking auth state
    if (authState == null) {
      return const MaterialApp(
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return MaterialApp.router(
      title: 'GaadiSewa+',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
      restorationScopeId: 'gaadi_sewa_app',
    );
  }
}



class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Welcome to GaadiSewa+'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Navigate to login screen
              },
              child: const Text('Get Started'),
            ),
          ],
        ),
      ),
    );
  }
}
