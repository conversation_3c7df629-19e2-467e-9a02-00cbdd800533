# GaadiSewa+ API Documentation

This document provides detailed information about the GaadiSewa+ API endpoints, request/response formats, and authentication mechanisms.

## Base URL

All API requests should be made to:
```
https://[YOUR_SUPABASE_REF].supabase.co/rest/v1/
```

## Authentication

GaadiSewa+ uses Supabase Auth with JWT tokens for authentication.

### Headers

```
Authorization: Bearer [JWT_TOKEN]
apikey: [YOUR_SUPABASE_ANON_KEY]
Content-Type: application/json
Prefer: return=minimal
```

## Database Tables

### Profiles

Stores user profile information.

**Table:** `profiles`

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key, references auth.users |
| full_name | text | User's full name |
| avatar_url | text | URL to user's avatar |
| phone | text | User's phone number |
| created_at | timestamptz | When the profile was created |
| updated_at | timestamptz | When the profile was last updated |

### Vehicles

Stores vehicle listings.

**Table:** `vehicles`

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| owner_id | uuid | References profiles.id |
| type | text | Vehicle type (car, bike, scooter, bicycle) |
| make | text | Vehicle make |
| model | text | Vehicle model |
| year | integer | Manufacturing year |
| color | text | Vehicle color |
| license_plate | text | License plate number |
| hourly_rate | numeric | Rental rate per hour |
| daily_rate | numeric | Rental rate per day |
| description | text | Vehicle description |
| is_available | boolean | Availability status |
| location | geography | Vehicle location (lat/lng) |
| features | jsonb | Array of features (AC, GPS, etc.) |
| images | text[] | Array of image URLs |
| created_at | timestamptz | When the vehicle was listed |
| updated_at | timestamptz | When the vehicle was last updated |

### Bookings

Stores booking information.

**Table:** `bookings`

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| vehicle_id | uuid | References vehicles.id |
| renter_id | uuid | References profiles.id |
| start_date | timestamptz | Booking start date |
| end_date | timestamptz | Booking end date |
| total_amount | numeric | Total booking amount |
| status | text | Booking status (pending, confirmed, cancelled, completed) |
| payment_status | text | Payment status (pending, paid, refunded) |
| payment_id | text | Payment gateway reference |
| created_at | timestamptz | When the booking was created |
| updated_at | timestamptz | When the booking was last updated |

### Messages

Stores in-app messages between users.

**Table:** `messages`

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| conversation_id | uuid | References conversations.id |
| sender_id | uuid | References profiles.id |
| content | text | Message content |
| is_read | boolean | Whether the message has been read |
| created_at | timestamptz | When the message was sent |
| updated_at | timestamptz | When the message was last updated |

### Conversations

Stores conversation metadata.

**Table:** `conversations`

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| participant1_id | uuid | References profiles.id |
| participant2_id | uuid | References profiles.id |
| last_message_id | uuid | References messages.id |
| created_at | timestamptz | When the conversation was created |
| updated_at | timestamptz | When the conversation was last updated |

## API Endpoints

### Authentication

#### Sign Up

```http
POST /auth/v1/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "options": {
    "data": {
      "full_name": "John Doe",
      "phone": "+1234567890"
    }
  }
}
```

#### Sign In

```http
POST /auth/v1/token?grant_type=password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### Vehicles

#### List Vehicles

```http
GET /rest/v1/vehicles?select=*
```

**Query Parameters:**
- `type`: Filter by vehicle type
- `min_price`: Minimum daily rate
- `max_price`: Maximum daily rate
- `location`: Filter by proximity (PostGIS)
- `order`: Sort order (e.g., `price.asc`, `created_at.desc`)
- `limit`: Number of results to return
- `offset`: Pagination offset

#### Get Vehicle by ID

```http
GET /rest/v1/vehicles?id=eq.[VEHICLE_ID]&select=*
```

#### Create Vehicle

```http
POST /rest/v1/vehicles
Content-Type: application/json

{
  "type": "car",
  "make": "Toyota",
  "model": "Corolla",
  "year": 2020,
  "color": "Blue",
  "license_plate": "ABC123",
  "hourly_rate": 10.00,
  "daily_rate": 80.00,
  "description": "Well-maintained sedan",
  "is_available": true,
  "location": "POINT(85.3240 27.7172)",
  "features": ["AC", "Bluetooth", "GPS"],
  "images": ["https://example.com/image1.jpg"]
}
```

### Bookings

#### Create Booking

```http
POST /rest/v1/bookings
Content-Type: application/json

{
  "vehicle_id": "[VEHICLE_UUID]",
  "start_date": "2023-06-01T10:00:00Z",
  "end_date": "2023-06-03T18:00:00Z",
  "total_amount": 160.00,
  "status": "pending",
  "payment_status": "pending"
}
```

#### Get User Bookings

```http
GET /rest/v1/bookings?renter_id=eq.[USER_ID]&select=*,vehicle:vehicles(*)
```

### Messages

#### Send Message

```http
POST /rest/v1/messages
Content-Type: application/json

{
  "conversation_id": "[CONVERSATION_UUID]",
  "sender_id": "[SENDER_UUID]",
  "content": "Hello, is this vehicle still available?",
  "is_read": false
}
```

#### Get Conversation Messages

```http
GET /rest/v1/messages?conversation_id=eq.[CONVERSATION_UUID]&order=created_at.asc
```

## Real-time Subscriptions

GaadiSewa+ uses Supabase Realtime for real-time updates. Example subscription:

```dart
final subscription = supabase
    .from('messages')
    .on('INSERT', (payload) {
      // Handle new message
      print('New message: ${payload.new}');
    })
    .subscribe();
```

## Error Handling

Standard HTTP status codes are used:

- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

Error responses include a JSON object with an `error` field containing details.

## Rate Limiting

API requests are rate limited to prevent abuse. If you exceed the limit, you'll receive a 429 status code.

## Webhooks

Webhooks are available for the following events:

- `booking.created`: Triggered when a new booking is created
- `booking.updated`: Triggered when a booking is updated
- `payment.succeeded`: Triggered when a payment is successful
- `payment.failed`: Triggered when a payment fails

## Changelog

### v1.0.0 (2023-05-31)
- Initial API release
- Authentication endpoints
- Vehicle management
- Booking system
- In-app messaging
- Real-time updates
