# Utility Classes

This document provides an overview of the utility classes used throughout the GaadiSewa+ application.

## Core Utilities

### 1. API Utils (`api_utils.dart`)
Handles API requests and responses, including error handling and token management.

**Key Features:**
- Singleton pattern for global access
- Automatic token refresh
- Error handling and logging
- Request/Response interceptors
- Support for different HTTP methods

**Usage Example:**
```dart
final response = await apiUtils.get('/api/endpoint');
if (response.isRight()) {
  // Handle success
} else {
  // Handle error
}
```

### 2. Localization Utils (`localization_utils.dart`)
Manages app localization and language settings.

**Key Features:**
- Supports multiple languages (English, Nepali)
- RTL/LTR text direction support
- Locale persistence
- Dynamic language switching

**Usage Example:**
```dart
// Set app locale
await localizationUtils.setLocale(const Locale('ne', 'NP'));

// Get current locale
final currentLocale = localizationUtils.currentLocale;
```

### 3. Image Utils (`image_utils.dart`)
Handles image-related operations including picking, compressing, and processing.

**Key Features:**
- Image picking from gallery/camera
- Image compression
- Thumbnail generation
- File format conversion

**Usage Example:**
```dart
// Pick image from gallery
final image = await imageUtils.pickImageFromGallery();

// Compress image
final compressedImage = await imageUtils.compressImage(image);
```

### 4. String Utils (`string_utils.dart`)
Provides common string manipulation and validation methods.

**Key Methods:**
- `isNullOrEmpty` - Checks if string is null or empty
- `capitalize` - Capitalizes first letter
- `truncate` - Truncates string with ellipsis
- `isValidEmail` - Validates email format
- `formatNumber` - Formats numbers with thousand separators

**Usage Example:**
```dart
final name = 'john doe';
print(StringUtils.capitalize(name)); // 'John doe'
print(StringUtils.capitalizeWords(name)); // 'John Doe'
```

### 5. Date/Time Utils (`date_time_utils.dart`)
Handles date and time formatting and calculations.

**Key Features:**
- Date formatting
- Time zone conversion
- Date manipulation (add/subtract days, months, etc.)
- Duration formatting

**Usage Example:**
```dart
final now = DateTime.now();
final formatted = DateTimeUtils.formatDate(now, 'yyyy-MM-dd');
final tomorrow = DateTimeUtils.addDays(now, 1);
```

### 6. Currency Utils (`currency_utils.dart`)
Handles currency formatting and conversions.

**Key Features:**
- Currency symbol formatting
- Exchange rate conversion
- Price formatting
- Tax calculations

**Usage Example:**
```dart
final price = 1000;
final formatted = CurrencyUtils.format(price); // 'Rs. 1,000'
final withSymbol = CurrencyUtils.formatWithSymbol(price, 'NPR'); // 'रु 1,000'
```

### 7. Validation Utils (`validation_utils.dart`)
Provides common validation methods for form inputs.

**Key Validations:**
- Email validation
- Phone number validation
- Password strength
- Required field validation
- Numeric validation

**Usage Example:**
```dart
final email = '<EMAIL>';
if (ValidationUtils.isValidEmail(email)) {
  // Valid email
}
```

## Best Practices

1. **Singleton Pattern**: Most utility classes follow the singleton pattern for consistent state management.
2. **Error Handling**: All utilities include proper error handling and logging.
3. **Performance**: Heavy operations (like image processing) are performed asynchronously.
4. **Testing**: Each utility class has corresponding test cases.
5. **Documentation**: All public methods are documented with clear usage examples.

## Adding New Utilities

When adding a new utility class:

1. Place it in the appropriate `lib/core/utils/` directory
2. Follow the existing patterns and conventions
3. Add comprehensive documentation
4. Include unit tests
5. Update this documentation

## Dependencies

- `intl` - For internationalization and formatting
- `image_picker` - For image selection
- `shared_preferences` - For local storage of settings
- `dartz` - For functional programming constructs
- `logger` - For consistent logging

## See Also

- [Architecture Documentation](./ARCHITECTURE.md)
- [API Documentation](./API.md)
