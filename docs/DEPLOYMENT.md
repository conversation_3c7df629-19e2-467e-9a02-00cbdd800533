# GaadiSewa+ Deployment Guide

This guide provides comprehensive instructions for deploying GaadiSewa+ across different environments, including development, staging, and production.

## Table of Contents

- [Environments](#-environments)
- [Prerequisites](#-prerequisites)
- [Configuration Management](#-configuration-management)
- [Building for Production](#-building-for-production)
- [Web Deployment](#-web-deployment)
- [Mobile Deployment](#-mobile-deployment)
- [Backend Deployment](#-backend-deployment)
- [CI/CD Pipeline](#-cicd-pipeline)
- [Environment Variables](#-environment-variables)
- [Monitoring and Logging](#-monitoring-and-logging)
- [Rollback Strategy](#-rollback-strategy)
- [Troubleshooting](#-troubleshooting)

## 🌍 Environments

We maintain three main environments:

1. **Development** (`dev`)
   - For active development
   - Latest changes from `develop` branch
   - Debug mode enabled
   - Connected to development Supabase project

2. **Staging** (`staging`)
   - For testing and QA
   - Matches production configuration
   - Connected to staging Supabase project
   - Accessible to internal team only

3. **Production** (`prod`)
   - Live environment for end-users
   - Only stable, tested releases
   - Connected to production Supabase project
   - Optimized for performance

## 📋 Prerequisites

### Development Machine
- Flutter SDK (3.19.0 or higher)
- Dart SDK (3.3.0 or higher)
- Android Studio / Xcode (for mobile builds)
- Firebase CLI (for web deployment)
- Supabase CLI

### Accounts
- GitHub account with repository access
- Firebase project for web deployment
- Google Play Developer account (Android)
- Apple Developer account (iOS)
- Supabase projects for each environment
- Khalti merchant account (payments)
- Mapbox account (maps)

## ⚙️ Configuration Management

### Environment Variables

Create `.env` files for each environment:

```bash
# .env.development
APP_ENV=development
DEBUG=true
SUPABASE_URL=your_dev_supabase_url
SUPABASE_ANON_KEY=your_dev_anon_key
KHALTI_PUBLIC_KEY=your_dev_khalti_key
MAPBOX_ACCESS_TOKEN=your_dev_mapbox_token
```

### Build Configuration

Update `lib/core/constants/app_constants.dart`:

```dart
class AppConstants {
  static const String env = String.fromEnvironment('APP_ENV');
  static const bool isProduction = env == 'production';
  static const bool isStaging = env == 'staging';
  static const bool isDevelopment = !isProduction && !isStaging;
  
  // Supabase
  static const String supabaseUrl = String.fromEnvironment('SUPABASE_URL');
  static const String supabaseAnonKey = String.fromEnvironment('SUPABASE_ANON_KEY');
  
  // Other configurations...
}
```

## 🏗 Building for Production

### Web

```bash
# Build for production
flutter build web \
  --release \
  --web-renderer html \
  --dart-define=APP_ENV=production \
  --dart-define=SUPABASE_URL=your_prod_supabase_url \
  --dart-define=SUPABASE_ANON_KEY=your_prod_anon_key \
  --dart-define=KHALTI_PUBLIC_KEY=your_prod_khalti_key \
  --dart-define=MAPBOX_ACCESS_TOKEN=your_prod_mapbox_token

# The built files will be in build/web/
```

### Android

```bash
# Build APK
flutter build apk \
  --release \
  --dart-define=APP_ENV=production \
  --dart-define=SUPABASE_URL=your_prod_supabase_url \
  --dart-define=SUPABASE_ANON_KEY=your_prod_anon_key \
  --dart-define=KHALTI_PUBLIC_KEY=your_prod_khalti_key \
  --dart-define=MAPBOX_ACCESS_TOKEN=your_prod_mapbox_token

# Build App Bundle
flutter build appbundle \
  --release \
  --dart-define=APP_ENV=production \
  --dart-define=SUPABASE_URL=your_prod_supabase_url \
  --dart-define=SUPABASE_ANON_KEY=your_prod_anon_key \
  --dart-define=KHALTI_PUBLIC_KEY=your_prod_khalti_key \
  --dart-define=MAPBOX_ACCESS_TOKEN=your_prod_mapbox_token
```

### iOS

```bash
# Build IPA
flutter build ipa \
  --release \
  --export-options-plist=ios/exportOptions.plist \
  --dart-define=APP_ENV=production \
  --dart-define=SUPABASE_URL=your_prod_supabase_url \
  --dart-define=SUPABASE_ANON_KEY=your_prod_anon_key \
  --dart-define=KHALTI_PUBLIC_KEY=your_prod_khalti_key \
  --dart-define=MAPBOX_ACCESS_TOKEN=your_prod_mapbox_token
```

## 🌐 Web Deployment

### Firebase Hosting

1. Install Firebase CLI if not already installed:
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. Initialize Firebase (if not already done):
   ```bash
   firebase init hosting
   ```

3. Deploy to Firebase:
   ```bash
   # Build first
   flutter build web --release --web-renderer html --dart-define-from-file=.env.production
   
   # Deploy
   firebase deploy --only hosting
   ```

### Environment-Specific Deployments

```bash
# Staging
firebase use staging
firebase deploy --only hosting:staging

# Production
firebase use production
firebase deploy --only hosting:production
```

## 📱 Mobile Deployment

### Android (Google Play Store)

1. Create a keystore (if not exists):
   ```bash
   keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

2. Configure signing in `android/app/build.gradle`:
   ```gradle
   android {
       signingConfigs {
           release {
               keyAlias upload
               keyPassword "your-keystore-password"
               storeFile file("../upload-keystore.jks")
               storePassword "your-keystore-password"
           }
       }
       buildTypes {
           release {
               signingConfig signingConfigs.release
           }
       }
   }
   ```

3. Build the app bundle:
   ```bash
   flutter build appbundle --release
   ```

4. Upload to Google Play Console:
   - Go to [Google Play Console](https://play.google.com/console)
   - Create a new release
   - Upload the app bundle from `build/app/outputs/bundle/release/app-release.aab`
   - Fill in release notes
   - Review and roll out

### iOS (App Store Connect)

1. Update version and build number in `pubspec.yaml`
2. Update app icons and splash screens
3. Build the IPA:
   ```bash
   flutter build ipa --export-options-plist=ios/exportOptions.plist
   ```
4. Open Xcode and archive the app:
   ```bash
   open ios/Runner.xcworkspace
   ```
   - Select "Any iOS Device" as the target
   - Go to Product > Archive
   - After archiving, click "Distribute App"
   - Follow the prompts to upload to App Store Connect

5. Complete the submission in App Store Connect:
   - Log in to [App Store Connect](https://appstoreconnect.apple.com/)
   - Create a new app version
   - Upload the build
   - Fill in app information, screenshots, and metadata
   - Submit for review

## 🚀 Backend Deployment (Supabase)

### Database Migrations

1. Create a new migration:
   ```bash
   supabase migration new create_vehicles_table
   ```

2. Apply migrations to an environment:
   ```bash
   # Development
   supabase db push --db-url "$SUPABASE_DB_URL"
   
   # Production
   supabase db push --db-url "$PROD_SUPABASE_DB_URL"
   ```

### Environment Configuration

1. Set up environment variables in Supabase:
   - Go to Project Settings > API
   - Configure CORS origins
   - Set up JWT secret
   - Configure authentication providers

2. Set up Row Level Security (RLS):
   ```sql
   -- Example: Enable RLS on vehicles table
   ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
   
   -- Create policies
   CREATE POLICY "Enable read access for all users"
   ON public.vehicles
   FOR SELECT
   USING (true);
   ```

## 🔄 CI/CD Pipeline

We use GitHub Actions for CI/CD. The workflow files are in `.github/workflows/`.

### Main Workflows

1. **CI (Continuous Integration)**
   - Runs on every push and PR
   - Lints code
   - Runs tests
   - Builds the app

2. **CD (Continuous Deployment)**
   - Runs when code is merged to `main` or `develop`
   - Deploys to Firebase Hosting
   - Updates Supabase (if needed)
   - Triggers mobile app builds

### Manual Deployment

To trigger a manual deployment:

1. Create a new release in GitHub
2. Tag it with the version number (e.g., `v1.0.0`)
3. The CD workflow will automatically deploy the tagged version

## 🔍 Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `APP_ENV` | Yes | `development`, `staging`, or `production` |
| `SUPABASE_URL` | Yes | Supabase project URL |
| `SUPABASE_ANON_KEY` | Yes | Supabase anonymous key |
| `KHALTI_PUBLIC_KEY` | Yes | Khalti public key for payments |
| `MAPBOX_ACCESS_TOKEN` | Yes | Mapbox access token for maps |
| `SENTRY_DSN` | No | Sentry DSN for error tracking |
| `MIXPANEL_TOKEN` | No | Mixpanel token for analytics |

## 📊 Monitoring and Logging

### Error Tracking

We use Sentry for error tracking. To set it up:

1. Create a project in [Sentry](https://sentry.io/)
2. Add your DSN to the environment variables
3. Errors will be automatically captured

### Analytics

We use Mixpanel for analytics. To set it up:

1. Create a project in [Mixpanel](https://mixpanel.com/)
2. Add your token to the environment variables
3. Events will be automatically tracked

### Logging

For server-side logging, check Supabase logs:

```bash
# View logs
supabase logs

# Filter logs
supabase logs --filter "type=postgres"
```

## 🔙 Rollback Strategy

### Web

1. Revert to a previous deployment in Firebase:
   ```bash
   firebase hosting:rollback
   ```

2. Or deploy a specific version:
   ```bash
   git checkout v1.0.0
   flutter build web --release
   firebase deploy --only hosting
   ```

### Mobile

1. **Android**:
   - Upload a new version with a higher version code
   - Roll out gradually to users
   
2. **iOS**:
   - Submit a new version to App Store Connect
   - Request an expedited review if critical

### Database

1. Use Supabase's point-in-time recovery:
   ```bash
   # Restore from a backup
   supabase db restore '2023-01-01 12:00:00+00:00'
   ```

2. Or run a manual SQL rollback:
   ```sql
   BEGIN;
   -- Rollback statements
   ROLLBACK;
   ```

## 🐛 Troubleshooting

### Common Issues

1. **Build Failures**
   - Clean the build: `flutter clean`
   - Get packages: `flutter pub get`
   - Check for version conflicts

2. **Environment Variables Not Loading**
   - Ensure `.env` files are in the root directory
   - Check for typos in variable names
   - Restart the IDE

3. **Supabase Connection Issues**
   - Check if Supabase is up: [status.supabase.com](https://status.supabase.com/)
   - Verify API keys and URLs
   - Check CORS settings in Supabase

4. **Payment Gateway Issues**
   - Verify Khalti API keys
   - Check network requests in browser dev tools
   - Test with test credentials first

### Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](/docs/TROUBLESHOOTING.md)
2. Search existing issues
3. Open a new issue with details:
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots/logs
   - Environment details

## 📚 Resources

- [Flutter Deployment](https://flutter.dev/docs/deployment/flavors)
- [Firebase Hosting](https://firebase.google.com/docs/hosting)
- [Supabase Docs](https://supabase.com/docs)
- [Google Play Console](https://play.google.com/console)
- [App Store Connect](https://appstoreconnect.apple.com/)
- [GitHub Actions](https://docs.github.com/en/actions)
