# GaadiSewa+ Development Guide

This guide provides detailed instructions for setting up the GaadiSewa+ development environment, coding standards, and development workflow.

## 🛠 Development Setup

### Prerequisites

- Flutter SDK (3.19.0 or higher)
- Dart SDK (3.3.0 or higher)
- Android Studio / Xcode (for emulators)
- VS Code (recommended) or Android Studio
- Git
- Supabase account
- Khalti merchant account (for payment testing)

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/gaadi-sewa.git
   cd gaadi-sewa
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Update the `.env` file with your configuration:
   ```
   # Supabase
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   
   # Khalti Payment Gateway
   KHALTI_PUBLIC_KEY=your_khalti_public_key
   
   # Mapbox (for maps and location services)
   MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
   ```

4. **Run the app**
   ```bash
   # For development
   flutter run --dart-define-from-file=.env
   
   # For web development
   flutter run -d chrome --web-renderer html --dart-define-from-file=.env
   ```

## 🏗 Project Structure

```
lib/
├── core/                  # Core functionality and utilities
│   ├── constants/        # App-wide constants
│   ├── errors/           # Error handling and failure types
│   ├── services/         # Core services (e.g., API clients)
│   ├── theme/            # App theming
│   └── utils/            # Utility functions and extensions
│
├── features/            # Feature modules (each feature is self-contained)
│   ├── auth/             # Authentication
│   │   ├── data/         # Data layer
│   │   ├── domain/       # Business logic
│   │   └── presentation/ # UI layer
│   │
│   ├── vehicles/        # Vehicle listings and management
│   ├── bookings/         # Booking management
│   ├── payments/         # Payment processing
│   ├── messages/         # In-app messaging
│   └── profile/          # User profiles
│
├── shared/              # Shared widgets and components
│   ├── widgets/          # Reusable widgets
│   └── models/           # Shared models
│
└── main.dart           # Application entry point
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
flutter test

# Run tests in a specific file
flutter test test/features/auth/presentation/bloc/auth_bloc_test.dart

# Run tests with coverage
flutter test --coverage
```

### Test Structure

- Unit tests: Test individual functions, methods, or classes
- Widget tests: Test individual widgets
- Integration tests: Test complete features or flows

## 🔄 State Management

We use Riverpod for state management throughout the app. Follow these guidelines:

- Use `StateNotifierProvider` for complex state logic
- Use `Provider` for simple values or dependencies
- Use `FutureProvider` for async data loading
- Use `StreamProvider` for real-time data

## 📱 UI/UX Guidelines

### Design System

- Follow Material Design 3 guidelines
- Use the theme defined in `lib/core/theme/`
- Use responsive design principles
- Support both light and dark themes

### Widget Guidelines

- Break down complex UIs into smaller, reusable widgets
- Use `const` constructors where possible
- Keep widget trees shallow
- Use `Key`s for dynamic lists and stateful widgets

## 🔒 Security

### Data Protection

- Never commit sensitive data to version control
- Use environment variables for configuration
- Validate all user inputs
- Sanitize data before displaying

### Authentication

- Use Supabase Auth for authentication
- Implement proper session management
- Handle token refresh automatically
- Secure sensitive routes

## 🚀 Deployment

### Building for Production

```bash
# Build Android APK
flutter build apk --release --dart-define-from-file=.env

# Build Android App Bundle
flutter build appbundle --release --dart-define-from-file=.env

# Build iOS
flutter build ios --release --dart-define-from-file=.env

# Build Web
flutter build web --release --dart-define-from-file=.env
```

### App Store Submission

1. Update version in `pubspec.yaml`
2. Update changelog in `CHANGELOG.md`
3. Build the release version
4. Submit to Google Play Store / Apple App Store

## 🔄 CI/CD

We use GitHub Actions for CI/CD. Workflow files are located in `.github/workflows/`.

## 📝 Documentation

### Code Documentation

- Document all public APIs
- Use DartDoc comments for classes and methods
- Keep documentation up-to-date

### API Documentation

API documentation is generated using `dartdoc`. To generate documentation:

```bash
flutter pub global activate dartdoc
dartdoc
```

## 🤝 Contributing

Please read our [Contributing Guide](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
