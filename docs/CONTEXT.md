# GaadiSewa+ - Project Context

## Overview
GaadiSewa+ is a peer-to-peer vehicle rental platform for urban Nepal, connecting vehicle owners with renters. The platform enables users to list, discover, and book vehicles for various durations, with a focus on affordability, convenience, and trust.

## Current State
- **Authentication System**: Fully implemented with email/password
- **Vehicle Management**: Users can list and discover vehicles with filtering
- **Search & Discovery**: Advanced search with filters for vehicle type, price, and location
- **State Management**: Robust state management using Riverpod
- **Backend**: Supabase integration with PostgreSQL database
- **UI/UX**: Responsive design with light/dark theme support
- **Navigation**: Intuitive app navigation with protected routes

## Key Features
- **Vehicle Listings**: Browse available vehicles with detailed information
- **Advanced Search**: Filter vehicles by type, price range, and location
- **Location-Based Discovery**: Find vehicles near you using GPS
- **User Profiles**: Manage personal information and rental history
- **Secure Authentication**: Email/password authentication with Supabase
- **Responsive Design**: Works on various screen sizes and devices

## Target Users
- **Students**: Affordable transportation for daily commutes
- **Office Workers**: Flexible travel options for work
- **Tourists**: Convenient local transportation while traveling
- **Vehicle Owners**: Monetize underutilized vehicles
- **Eco-conscious Individuals**: Sustainable transport alternatives
- **Businesses**: Fleet management for company vehicles

## Technology Stack
- **Frontend**:
  - Flutter (iOS & Android)
  - Riverpod for state management
  - GoRouter for navigation
  - Google Maps integration for location services

- **Backend (Supabase)**:
  - PostgreSQL Database
  - Authentication & Authorization
  - Realtime Updates
  - File Storage
  - Row Level Security (RLS)

- **APIs & Services**:
  - Geolocation services
  - Payment gateway integration (planned)
  - Push notifications (planned)

- **Development Tools**:
  - Flutter SDK
  - Android Studio / VS Code
  - GitHub for version control
  - Supabase CLI

## Market Focus
- **Initial Launch**: Kathmandu Valley (Q3 2025)
- **Phase 1 Expansion**: Pokhara, Chitwan, and Biratnagar (Q4 2025)
- **Phase 2 Expansion**: Major cities across Nepal (2026)
- **Target Segments**:
  - Urban commuters
  - College students
  - Tourists
  - Local businesses
  - Ride-sharing partners

## Competitive Advantage
- **Local Focus**: Tailored for Nepali market needs
- **Affordable**: Lower costs than traditional rental services
- **Flexible**: Hourly, daily, and weekly rental options
- **Trust & Safety**: Verified users and secure payment system
- **Sustainability**: Promotes shared vehicle usage

## Next Milestones
1. Implement booking system
2. Integrate payment processing
3. Develop user dashboard
4. Add review and rating system
5. Launch beta testing program
