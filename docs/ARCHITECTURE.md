# System Architecture

## Overview
GaadiSewa+ follows a client-server architecture with a Flutter mobile application interacting with a Supabase backend.

## Database Schema

### Tables
1. `profiles`
   - id (UUID, PK)
   - full_name (text)
   - phone (text)
   - avatar_url (text)
   - created_at (timestamp)

2. `vehicles`
   - id (UUID, PK)
   - owner_id (UUID, FK -> profiles.id)
   - type (enum: bicycle, scooter, car)
   - make (text)
   - model (text)
   - year (integer)
   - hourly_rate (numeric)
   - daily_rate (numeric)
   - location (geography)
   - is_available (boolean)

3. `bookings`
   - id (UUID, PK)
   - vehicle_id (UUID, FK -> vehicles.id)
   - renter_id (UUID, FK -> profiles.id)
   - start_time (timestamp)
   - end_time (timestamp)
   - total_cost (numeric)
   - status (enum: pending, confirmed, completed, cancelled)

## Flutter App Structure
```
lib/
├── main.dart
├── models/
├── screens/
│   ├── auth/
│   ├── home/
│   ├── search/
│   ├── bookings/
│   └── profile/
├── services/
│   ├── auth_service.dart
│   ├── booking_service.dart
│   └── vehicle_service.dart
├── widgets/
└── utils/
    ├── api_utils.dart
    ├── localization_utils.dart
    ├── image_utils.dart
    ├── string_utils.dart
    ├── date_time_utils.dart
    ├── currency_utils.dart
    └── validation_utils.dart
```

## Utility Classes

The application includes several utility classes that provide reusable functionality across the codebase. These utilities handle common tasks such as API communication, localization, image processing, and data validation.

For detailed documentation about each utility class, see [UTILS.md](./UTILS.md).

## Supabase Integration
- Authentication: Email/Password, Google, Facebook
- Realtime: Real-time booking updates
- Storage: Vehicle images, user avatars
- Row Level Security: Data access control
