# GaadiSewa+ Troubleshooting Guide

This guide provides solutions to common issues you might encounter while developing and running the GaadiSewa+ application.

## Table of Contents

- [Setup Issues](#-setup-issues)
- [Build and Run Issues](#-build-and-run-issues)
- [Dependency Issues](#-dependency-issues)
- [Supabase Issues](#-supabase-issues)
- [State Management Issues](#-state-management-issues)
- [UI/Widget Issues](#-uiwidget-issues)
- [Testing Issues](#-testing-issues)
- [Performance Issues](#-performance-issues)
- [Deployment Issues](#-deployment-issues)
- [Common Error Messages](#-common-error-messages)
- [Getting Help](#-getting-help)

## 🛠 Setup Issues

### Flutter/Dart SDK Not Found

**Issue**: VS Code shows "Flutter not found" or "Dart SDK not found" errors.

**Solution**:
1. Verify Flutter installation:
   ```bash
   flutter doctor -v
   ```
2. Set the correct Flutter SDK path in VS Code settings:
   - Open Command Palette (Ctrl+Shift+P or Cmd+Shift+P)
   - Type "Flutter: Configure Flutter SDK"
   - Select your Flutter SDK directory

### Environment Variables Not Loading

**Issue**: Environment variables from `.env` file are not being loaded.

**Solution**:
1. Ensure the `.env` file exists in the project root
2. Install required package:
   ```bash
   flutter pub add flutter_dotenv
   ```
3. Load environment variables in `main.dart`:
   ```dart
   import 'package:flutter_dotenv/flutter_dotenv.dart';
   
   Future<void> main() async {
     await dotenv.load(fileName: ".env");
     runApp(MyApp());
   }
   ```
4. For tests, use `--dart-define-from-file=.env`

## 🏗 Build and Run Issues

### Build Failures

**Issue**: Build fails with various errors.

**Solution**:
1. Clean and rebuild:
   ```bash
   flutter clean
   flutter pub get
   flutter pub run build_runner build --delete-conflicting-outputs
   flutter run
   ```
2. If using iOS, run:
   ```bash
   cd ios
   pod install
   cd ..
   ```

### App Crashes on Launch

**Issue**: The app builds but crashes immediately.

**Solution**:
1. Check logs:
   ```bash
   flutter run -v
   ```
2. Look for native crashes in device logs
3. Check for missing permissions in `AndroidManifest.xml` or `Info.plist`
4. Verify all required environment variables are set

## 📦 Dependency Issues

### Version Conflicts

**Issue**: Version solving failed or dependency conflicts.

**Solution**:
1. Check for the latest versions of packages
2. Run:
   ```bash
   flutter pub outdated
   flutter pub upgrade --major-versions
   ```
3. If issues persist, try:
   ```bash
   rm -rf pubspec.lock .dart_tool/
   flutter pub get
   ```

### Build Runner Issues

**Issue**: `build_runner` fails to generate files.

**Solution**:
1. Clean and regenerate:
   ```bash
   flutter clean
   flutter pub get
   flutter pub run build_runner clean
   flutter pub run build_runner build --delete-conflicting-outputs
   ```
2. If files are locked:
   ```bash
   flutter pub run build_runner build --delete-conflicting-outputs --release
   ```

## 🔌 Supabase Issues

### Connection Issues

**Issue**: Cannot connect to Supabase.

**Solution**:
1. Verify your Supabase URL and anon key
2. Check CORS settings in Supabase:
   ```sql
   -- In Supabase SQL Editor
   update storage.buckets set public = true where id = 'your-bucket-name';
   ```
3. Enable Row Level Security (RLS) if needed:
   ```sql
   -- Example for profiles table
   create policy "Public profiles are viewable by everyone."
     on profiles for select
     using ( true );
   ```

### Realtime Not Working

**Issue**: Real-time updates are not being received.

**Solution**:
1. Ensure you've enabled replication for your tables:
   ```sql
   -- In Supabase SQL Editor
   alter publication supabase_realtime add table your_table_name;
   ```
2. Check your subscription code:
   ```dart
   final subscription = supabase
       .from('your_table')
       .on(SupabaseEventTypes.all, (payload) {
         print('Change received! $payload');
       })
       .subscribe();
   ```
3. Don't forget to dispose of subscriptions:
   ```dart
   @override
   void dispose() {
     subscription.unsubscribe();
     super.dispose();
   }
   ```

## 🧩 State Management Issues

### State Not Updating

**Issue**: UI doesn't update when state changes.

**Solution**:
1. For Riverpod, ensure you're using `ref.watch` in your widgets:
   ```dart
   final state = ref.watch(yourProvider);
   ```
2. For StateNotifier, ensure you're emitting a new state:
   ```dart
   state = state.copyWith(yourChanges: newValue);
   ```
3. Check that your widget is wrapped in a `ConsumerWidget` or `ConsumerStatefulWidget`

### Provider Not Found

**Issue**: "Provider not found" error.

**Solution**:
1. Ensure the provider is declared at a higher level in the widget tree
2. For scoped providers, make sure you're within the scope
3. Check for typos in the provider name

## 🎨 UI/Widget Issues

### RenderFlex Overflow

**Issue**: "A RenderFlex overflowed by X pixels on the right/bottom."

**Solution**:
1. Wrap the overflowing widget in a `SingleChildScrollView`
2. Use `Expanded` or `Flexible` widgets
3. Consider using `ListView` for scrollable content
4. Check for hardcoded dimensions

### Layout Issues

**Issue**: Widgets not displaying as expected.

**Solution**:
1. Use `DebugPaintSizeEnabled` to visualize layouts:
   ```dart
   void main() {
     debugPaintSizeEnabled = true; // Add this line
     runApp(MyApp());
   }
   ```
2. Check widget constraints
3. Use `LayoutBuilder` for responsive designs

## 🧪 Testing Issues

### Tests Failing

**Issue**: Tests are failing unexpectedly.

**Solution**:
1. Run tests with verbose output:
   ```bash
   flutter test -v
   ```
2. Check for async operations that might need `pumpAndSettle`
3. Mock external dependencies
4. Ensure test environment is properly set up

### Mocking Issues

**Issue**: Problems with mocking in tests.

**Solution**:
1. Use `mocktail` for mocking:
   ```dart
   import 'package:mocktail/mocktail.dart';
   
   class MockSupabaseClient extends Mock implements SupabaseClient {}
   ```
2. Set up mock responses:
   ```dart
   when(() => mockClient.auth.signIn(
     email: any(named: 'email'),
     password: any(named: 'password'),
   )).thenAnswer((_) async => mockUser);
   ```

## ⚡ Performance Issues

### Slow UI

**Issue**: The UI is janky or slow.

**Solution**:
1. Run the performance overlay:
   ```dart
   MaterialApp(
     showPerformanceOverlay: true,
     // ...
   );
   ```
2. Use `const` constructors where possible
3. Implement `ListView.builder` for long lists
4. Use `RepaintBoundary` for complex animations

### Memory Leaks

**Issue**: App memory usage keeps increasing.

**Solution**:
1. Check for unclosed controllers:
   ```dart
   @override
   void dispose() {
     _controller.dispose();
     super.dispose();
   }
   ```
2. Use `WeakReference` for callbacks
3. Profile with DevTools Memory tab

## 🚀 Deployment Issues

### App Rejection

**Issue**: App was rejected from app stores.

**Solution**:
1. For iOS App Store:
   - Ensure all app icons are included
   - Add privacy descriptions in `Info.plist`
   - Include app store screenshots
2. For Google Play:
   - Complete all store listing requirements
   - Set up app signing
   - Test with release builds

### Build Size Too Large

**Issue**: The app bundle/APK is too large.

**Solution**:
1. Enable code shrinking and obfuscation:
   ```yaml
   # android/app/build.gradle
   buildTypes {
     release {
       signingConfig signingConfigs.release
       minifyEnabled true
       shrinkResources true
       proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
     }
   }
   ```
2. Remove unused resources
3. Use WebP images instead of PNG

## ❌ Common Error Messages

### "Target of URI doesn't exist"

**Solution**:
1. Run `flutter pub get`
2. Check for typos in import statements
3. Ensure the file exists at the specified path

### "Incorrect use of ParentDataWidget"

**Solution**:
1. Check that widgets like `Expanded` and `Flexible` are direct children of `Row`, `Column`, or `Flex`
2. Ensure proper widget hierarchy

### "setState() or markNeedsBuild() called during build"

**Solution**:
1. Use `WidgetsBinding.instance.addPostFrameCallback`:
   ```dart
   WidgetsBinding.instance.addPostFrameCallback((_) {
     setState(() {
       // Your state update here
     });
   });
   ```
2. Or use `Future.microtask`

### "Null check operator used on a null value"

**Solution**:
1. Check for null before using the null assertion operator (`!`)
2. Use null-aware operators (`?.`, `??`)
3. Initialize variables properly

## 🆘 Getting Help

### When to Ask for Help

- You've tried the solutions above
- You've searched existing issues
- You can reproduce the issue consistently
- You can provide steps to reproduce

### How to Ask for Help

1. Search existing issues first
2. Create a new issue with:
   - Clear title
   - Description of the problem
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots/videos if applicable
   - Flutter doctor output
   - Error logs

### Resources

- [Flutter Documentation](https://flutter.dev/docs)
- [Flutter Community](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [GitHub Issues](https://github.com/yourusername/gaadi-sewa/issues)

## 🎉 Contributing to This Guide

If you've found a solution that's not listed here, please consider contributing to this guide by submitting a pull request with your solution.
