# Git Workflow for GaadiSewa+

This document outlines the Git workflow and best practices for the GaadiSewa+ project to ensure a smooth development process and maintainable codebase.

## Table of Contents

- [Branching Strategy](#-branching-strategy)
- [Commit Message Guidelines](#-commit-message-guidelines)
- [Pull Request Process](#-pull-request-process)
- [Code Review Guidelines](#-code-review-guidelines)
- [Versioning Strategy](#-versioning-strategy)
- [Handling Hotfixes](#-handling-hotfixes)
- [Git Hooks](#-git-hooks)
- [Best Practices](#-best-practices)
- [Common Tasks](#-common-tasks)

## 🌿 Branching Strategy

We follow the [GitFlow](https://nvie.com/posts/a-successful-git-branching-model/) workflow with some modifications:

### Main Branches

1. **main**
   - Production-ready code
   - Protected branch (direct commits not allowed)
   - Each commit represents a production release
   - Tagged with version numbers (e.g., v1.0.0)

2. **develop**
   - Integration branch for features
   - Always in a deployable state
   - Merged into main for releases

### Supporting Branches

1. **Feature Branches**
   - `feature/feature-name`
   - Created from `develop`
   - Merged back to `develop`
   - Example: `feature/user-authentication`

2. **Bugfix Branches**
   - `bugfix/description`
   - Created from `develop`
   - Merged back to `develop`
   - Example: `bugfix/login-validation`

3. **Release Branches**
   - `release/v1.0.0`
   - Created from `develop`
   - Merged to `main` and `develop`
   - Used for final testing and preparation

4. **Hotfix Branches**
   - `hotfix/description`
   - Created from `main`
   - Merged to `main` and `develop`
   - Example: `hotfix/payment-processing`

## ✍️ Commit Message Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### Types

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

### Examples

```
feat(auth): add Google Sign-In

- Implement Google OAuth2 authentication
- Add user profile creation on first sign-in

Closes #123
```

```
fix(payments): handle declined cards gracefully

- Add proper error handling for declined cards
- Show user-friendly error messages

Fixes #456
```

## 🔄 Pull Request Process

1. **Create a Feature Branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   ```

2. **Make Your Changes**
   - Follow the code style
   - Write tests
   - Update documentation

3. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "feat(auth): add email verification"
   ```

4. **Push Your Changes**
   ```bash
   git push -u origin feature/your-feature-name
   ```

5. **Create a Pull Request**
   - Target: `develop` branch
   - Fill in the PR template
   - Request reviewers
   - Link related issues

6. **Address Review Comments**
   - Make requested changes
   - Push updates to the same branch
   - Resolve conversations

7. **Merge and Clean Up**
   - Squash and merge
   - Delete the feature branch
   - Update your local repository

## 👁️‍🗨️ Code Review Guidelines

### For Reviewers
- Be constructive and kind
- Focus on code, not the person
- Explain the "why" behind suggestions
- Acknowledge good practices
- Check for:
  - Code quality and style
  - Test coverage
  - Documentation
  - Security implications
  - Performance considerations

### For Authors
- Keep PRs focused and small
- Write clear commit messages
- Address all comments
- Update documentation
- Test your changes

## 🏷️ Versioning Strategy

We use [Semantic Versioning](https://semver.org/): `MAJOR.MINOR.PATCH`

- **MAJOR**: Incompatible API changes
- **MINOR**: Backward-compatible functionality
- **PATCH**: Backward-compatible bug fixes

### Release Process

1. Create a release branch
   ```bash
   git checkout -b release/v1.2.0 develop
   ```

2. Update version numbers
   - Update `pubspec.yaml`
   - Update `CHANGELOG.md`

3. Create a PR to `main`
   ```bash
   git add .
   git commit -m "chore(release): prepare v1.2.0 release"
   git push -u origin release/v1.2.0
   ```

4. After approval, merge to `main` and tag
   ```bash
   git checkout main
   git merge --no-ff release/v1.2.0
   git tag -a v1.2.0 -m "Version 1.2.0"
   git push origin main --tags
   ```

5. Merge back to `develop`
   ```bash
   git checkout develop
   git merge --no-ff release/v1.2.0
   git push origin develop
   ```

6. Delete the release branch
   ```bash
   git branch -d release/v1.2.0
   git push origin --delete release/v1.2.0
   ```

## 🔥 Handling Hotfixes

1. Create a hotfix branch
   ```bash
   git checkout -b hotfix/login-issue main
   ```

2. Make the necessary changes
   - Fix the critical issue
   - Add tests
   - Update documentation

3. Commit and push
   ```bash
   git add .
   git commit -m "fix(auth): resolve login issue with special characters"
   git push -u origin hotfix/login-issue
   ```

4. Create a PR to `main`
   - Get it reviewed
   - Merge to `main`
   - Tag the release
   - Merge to `develop`
   - Delete the hotfix branch

## 🪝 Git Hooks

We use Git hooks to maintain code quality. The following hooks are set up:

### Pre-commit
- Runs `dart format`
- Runs `dart analyze`
- Runs tests

### Commit Message
- Validates commit message format
- Ensures conventional commit style

### Installation
Run the setup script:
```bash
./setup.sh
```

## 🏆 Best Practices

### General
- Keep commits small and focused
- Write meaningful commit messages
- Pull frequently from the main branch
- Never commit sensitive data
- Use `.gitignore` effectively

### Branching
- Delete merged branches
- Keep branches up-to-date with main
- Use descriptive branch names
- Limit branch lifetime

### Code Review
- Keep PRs small and focused
- Use the PR template
- Request specific reviewers
- Address all comments

## 🔧 Common Tasks

### Start a New Feature
```bash
git checkout develop
git pull origin develop
git checkout -b feature/feature-name
```

### Update Your Branch
```bash
git checkout develop
git pull origin develop
git checkout your-branch
git merge develop
```

### Squash Commits
```bash
git rebase -i HEAD~3  # Squash last 3 commits
```

### Undo Last Commit
```bash
git reset --soft HEAD~1
```

### View Commit History
```bash
git log --oneline --graph --decorate --all
```

## 📚 Resources

- [Git Documentation](https://git-scm.com/doc)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [GitFlow Workflow](https://nvie.com/posts/a-successful-git-branching-model/)
- [GitHub Flow](https://guides.github.com/introduction/flow/)
- [Semantic Versioning](https://semver.org/)
