# GaadiSewa+ Style Guide

This style guide outlines the coding standards and best practices for the GaadiSewa+ project. Following these guidelines helps maintain code consistency, readability, and maintainability across the codebase.

## Table of Contents

- [General Principles](#-general-principles)
- [Dart/Flutter Style](#-dartflutter-style)
- [Project Structure](#-project-structure)
- [Naming Conventions](#-naming-conventions)
- [Code Organization](#-code-organization)
- [Documentation](#-documentation)
- [State Management](#-state-management)
- [Erro<PERSON>](#-error-handling)
- [Performance](#-performance)
- [Security](#-security)
- [Accessibility](#-accessibility)
- [Testing](#-testing)
- [Git Workflow](#-git-workflow)
- [Code Review](#-code-review)

## 🌟 General Principles

1. **Readability First**: Write code that is easy to read and understand.
2. **Consistency**: Follow the existing code style in the project.
3. **Simplicity**: Prefer simple, clear solutions over clever ones.
4. **Maintainability**: Write code that is easy to modify and extend.
5. **Performance**: Write efficient code, but not at the cost of readability.
6. **Testability**: Write code that is easy to test.

## 🎯 Dart/Flutter Style

### Formatting

- Use `dart format` to format all Dart code
- Line length: 80 characters (with reasonable exceptions)
- Use 2 spaces for indentation (not tabs)
- Use single quotes for strings unless escaping is needed
- Always use curly braces for control flow statements
- Add a trailing comma after the last parameter in a list of parameters

### Naming

- **Classes**: `PascalCase`
  ```dart
  class UserProfile {}
  ```

- **Variables and parameters**: `camelCase`
  ```dart
  String userName;
  void updateProfile(String newName) {}
  ```

- **Constants**: `lowerCamelCase` for regular constants, `UPPER_CASE_WITH_UNDERSCORES` for compile-time constants
  ```dart
  const maxRetryCount = 3;
  static const MAX_RETRY_COUNT = 3;
  ```

- **Private members**: `_leadingUnderscore`
  ```dart
  String _privateField;
  void _privateMethod() {}
  ```

### Imports

- Group and order imports as follows:
  1. Dart/Flutter SDK imports
  2. Package imports
  3. Project imports
- Use relative imports for files in the same package
- Use `package:` imports for files in other packages
- Add a blank line between sections

```dart
// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

// Project imports
import 'package:gaadi_sewa/core/constants/app_constants.dart';
import 'package:gaedi_sewa/features/auth/presentation/widgets/login_form.dart';
```

## 🏗 Project Structure

Follow the feature-first structure:

```
lib/
  features/
    feature_name/
      data/
        datasources/
        models/
        repositories/
      domain/
        entities/
        repositories/
        usecases/
      presentation/
        bloc/
        pages/
        widgets/
  core/
    constants/
    errors/
    network/
    theme/
    utils/
  shared/
    widgets/
    models/
    services/
```

## 📝 Documentation

### Documentation Comments

Use Dart's documentation comments (`///`) for public APIs:

```dart
/// A user profile containing personal information.
///
/// This class represents the user profile with basic information
/// like name, email, and profile picture.
class UserProfile {
  /// The user's full name.
  final String name;

  /// Creates a [UserProfile] with the given [name] and [email].
  const UserProfile({
    required this.name,
    required this.email,
  });
}
```

### Code Comments

- Use `//` for inline comments
- Explain why, not what
- Keep comments up-to-date
- Remove commented-out code

## 🏗 State Management

We use Riverpod for state management. Follow these guidelines:

1. **Provider Naming**:
   - Use `[name]Provider` for providers
   - Use `[name]Notifier` for notifiers
   - Use `[name]State` for state classes

2. **State Classes**:
   - Make them immutable using `@immutable`
   - Implement `copyWith` for updates
   - Override `toString()` for debugging

```dart
@immutable
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? error;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.error,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? error,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      error: error,
    );
  }

  @override
  String toString() => 'AuthState(status: $status, user: $user, error: $error)';
}
```

## 🚨 Error Handling

1. **Use Custom Exceptions**:
   ```dart
   class ServerException implements Exception {
     final String message;
     final int statusCode;

     const ServerException(this.message, {this.statusCode = 500});

     @override
     String toString() => 'ServerException: $message ($statusCode)';
   }
   ```

2. **Use Either for Operations That Can Fail**:
   ```dart
   Future<Either<Failure, User>> getUser(String id) async {
     try {
       final user = await remoteDataSource.getUser(id);
       return Right(user);
     } on ServerException catch (e) {
       return Left(ServerFailure(e.message));
     }
   }
   ```

## ⚡ Performance

1. **Widget Optimization**:
   - Use `const` constructors
   - Use `const` widgets where possible
   - Use `ListView.builder` for long lists
   - Avoid unnecessary rebuilds with `const` constructors and `const` widgets

2. **Image Optimization**:
   - Use appropriate image formats (WebP, SVG)
   - Cache images
   - Use `cached_network_image` for remote images

## 🔒 Security

1. **Secrets Management**:
   - Never commit secrets to version control
   - Use environment variables for configuration
   - Use `flutter_dotenv` for local development

2. **Input Validation**:
   - Validate all user inputs
   - Use parameterized queries for database operations
   - Sanitize data before displaying

## ♿ Accessibility

1. **Semantic Widgets**:
   - Use semantic widgets (`Semantics`, `MergeSemantics`)
   - Add semantic labels to interactive elements
   - Support screen readers

2. **Text Scaling**:
   - Use relative font sizes
   - Avoid fixed dimensions for text containers
   - Test with different text scaling factors

## 🧪 Testing

1. **Test Naming**:
   - `test('should [expected behavior] when [state]')`
   - `testWidgets('WidgetName should [expected behavior]')`

2. **Test Structure**:
   - Use `setUp` and `tearDown`
   - Group related tests
   - Use descriptive test names

## 🌿 Git Workflow

1. **Branch Naming**:
   - `feature/feature-name` for new features
   - `bugfix/description` for bug fixes
   - `hotfix/description` for critical fixes
   - `chore/description` for maintenance tasks

2. **Commit Messages**:
   - Use the conventional commit format:
     ```
     <type>(<scope>): <description>
     
     [optional body]
     
     [optional footer]
     ```
   - Types: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `chore`

## 👁️‍🗨️ Code Review

1. **Review Checklist**:
   - [ ] Code follows the style guide
   - [ ] Tests are included and pass
   - [ ] Documentation is updated
   - [ ] No commented-out code
   - [ ] No debug prints
   - [ ] No sensitive data is exposed

2. **Review Comments**:
   - Be constructive and specific
   - Suggest improvements, not just point out issues
   - Acknowledge good practices

## 📚 Additional Resources

- [Effective Dart](https://dart.dev/guides/language/effective-dart)
- [Flutter Style Guide](https://github.com/flutter/flutter/wiki/Style-guide-for-Flutter-repo)
- [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- [Flutter Best Practices](https://flutter.dev/docs/development/tools/devtools/inspector/best-practices)
- [Riverpod Documentation](https://riverpod.dev/)

## 📝 License

This style guide is part of the GaadiSewa+ project and is licensed under the MIT License.
