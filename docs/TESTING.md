# Testing Guide

This guide provides comprehensive information about testing in the GaadiSewa+ project, including testing strategies, tools, and best practices.

## Table of Contents

- [Testing Strategy](#-testing-strategy)
- [Test Types](#-test-types)
- [Test Structure](#-test-structure)
- [Running Tests](#-running-tests)
- [Test Coverage](#-test-coverage)
- [Writing Tests](#-writing-tests)
- [Test Data Management](#-test-data-management)
- [Continuous Integration](#-continuous-integration)
- [Best Practices](#-best-practices)
- [Troubleshooting](#-troubleshooting)

## 🎯 Testing Strategy

We follow a multi-layered testing approach to ensure code quality and reliability:

1. **Unit Tests**: Test individual functions and methods in isolation
2. **Widget Tests**: Test individual widgets
3. **Integration Tests**: Test complete features or user flows
4. **Golden Tests**: Test UI components against golden files
5. **Manual Testing**: Manual verification of critical user journeys

## 🧪 Test Types

### 1. Unit Tests

- **Location**: `test/unit/`
- **Purpose**: Test business logic in isolation
- **Tools**: `test` package, `mocktail` for mocking
- **Coverage Target**: 80%+

### 2. Widget Tests

- **Location**: `test/widget/`
- **Purpose**: Test individual widgets
- **Tools**: `flutter_test` package
- **Coverage Target**: 70%+

### 3. Integration Tests

- **Location**: `integration_test/`
- **Purpose**: Test complete features and user flows
- **Tools**: `integration_test` package
- **Coverage Target**: 60%+

### 4. Golden Tests

- **Location**: `test/golden/`
- **Purpose**: Test UI components against golden files
- **Tools**: `golden_toolkit`
- **Coverage Target**: 50% of UI components

## 🏗 Test Structure

```
test/
├── unit/
│   ├── features/
│   │   ├── auth/
│   │   │   └── auth_bloc_test.dart
│   │   └── vehicles/
│   │       └── vehicle_repository_test.dart
│   └── shared/
│       └── utils/
│           └── validators_test.dart
├── widget/
│   └── features/
│       └── auth/
│           └── login_form_test.dart
└── golden/
    └── widgets/
        └── primary_button_test.dart
```

## 🚀 Running Tests

### Run All Tests

```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Generate HTML coverage report
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

### Run Specific Tests

```bash
# Run all tests in a specific file
flutter test test/unit/features/auth/auth_bloc_test.dart

# Run a specific test by name
flutter test --plain-name "test description"

# Run tests with a specific tag
flutter test --tags=integration
```

### Run Integration Tests

```bash
# Run on connected device/emulator
flutter test integration_test/app_test.dart -d <device_id>

# Run on Chrome
flutter test integration_test/app_test.dart -d chrome
```

## 📊 Test Coverage

### Generate Coverage Report

```bash
# Run tests with coverage
flutter test --coverage

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# Open the report
open coverage/html/index.html
```

### Coverage Reports in CI

Coverage reports are automatically generated in CI and can be viewed in the GitHub Actions workflow summary.

## ✍️ Writing Tests

### Unit Test Example

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:gaadi_sewa/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:gaadi_sewa/features/auth/domain/repositories/auth_repository.dart';

class MockSupabaseClient extends Mock implements SupabaseClient {}

void main() {
  late AuthRepository repository;
  late MockSupabaseClient mockClient;

  setUp(() {
    mockClient = MockSupabaseClient();
    repository = AuthRepositoryImpl(mockClient);
  });

  group('signInWithEmail', () {
    test('should return User when sign in is successful', () async {
      // Arrange
      when(() => mockClient.auth.signIn(
        email: '<EMAIL>',
        password: 'password123',
      )).thenAnswer((_) async => MockUser());

      // Act
      final result = await repository.signInWithEmail(
        email: '<EMAIL>',
        password: 'password123',
      );

      // Assert
      expect(result, isA<User>());
      verify(() => mockClient.auth.signIn(
        email: '<EMAIL>',
        password: 'password123',
      )).called(1);
    });
  });
}
```

### Widget Test Example

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gaadi_sewa/features/auth/presentation/widgets/login_form.dart';

void main() {
  testWidgets('LoginForm shows validation errors', (WidgetTester tester) async {
    // Build our widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: LoginForm(),
        ),
      ),
    );

    // Tap the login button without entering any text
    await tester.tap(find.byType(ElevatedButton));
    await tester.pump();

    // Verify that the validation errors are shown
    expect(find.text('Please enter your email'), findsOneWidget);
    expect(find.text('Please enter your password'), findsOneWidget);
  });
}
```

### Integration Test Example

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:gaadi_sewa/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Complete booking flow', (WidgetTester tester) async {
    // Launch the app
    app.main();
    await tester.pumpAndSettle();

    // Verify we're on the login screen
    expect(find.text('Welcome Back'), findsOneWidget);

    // Enter credentials and tap login
    await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
    await tester.enterText(find.byType(TextFormField).last, 'password123');
    await tester.tap(find.text('Sign In'));
    await tester.pumpAndSettle();

    // Verify we're on the home screen
    expect(find.text('Available Vehicles'), findsOneWidget);

    // Continue with the rest of the test...
  });
}
```

## 📋 Test Data Management

### Test Data Factories

We use the `factory_bot` package to generate test data:

```dart
// test/factories/vehicle_factory.dart
import 'package:gaadi_sewa/features/vehicles/domain/entities/vehicle.dart';
import 'package:factory_bot/factory_bot.dart';

extend Factory<Vehicle> with VehicleFactory;

class VehicleFactory extends Factory with FactoryMixin {
  @override
  Map<String, dynamic> generate() {
    return {
      'id': f.guid.guid(),
      'make': f.vehicle.make(),
      'model': f.vehicle.model(),
      'year': f.random.integer(10, min: 2010),
      'hourlyRate': f.finance.amount().toDouble(),
      'isAvailable': true,
    };
  }
}
```

### Fixtures

Store test data in JSON files in the `test/fixtures/` directory:

```json
// test/fixtures/vehicles.json
{
  "valid_vehicle": {
    "id": "550e8400-e29b-41d4-a716-************",
    "make": "Toyota",
    "model": "Corolla",
    "year": 2020,
    "hourlyRate": 10.0,
    "isAvailable": true
  }
}
```

## 🔄 Continuous Integration

### GitHub Actions

We use GitHub Actions for CI/CD. The workflow is defined in `.github/workflows/`:

- `test.yml`: Runs tests on every push and PR
- `deploy.yml`: Handles deployment to production

### Test Matrix

Tests run on:
- Latest stable Flutter version
- All supported platforms (Android, iOS, Web)
- Multiple OS versions

## 🏆 Best Practices

### General

- Write tests before or alongside the code (TDD)
- Keep tests independent and isolated
- Test one thing per test case
- Use descriptive test names
- Follow the Arrange-Act-Assert pattern
- Keep tests fast and reliable

### Naming Conventions

- Test files: `[name]_test.dart`
- Test groups: `group('ClassName', () { ... })`
- Test cases: `test('should do something when something happens', () { ... })`
- Widget tests: `testWidgets('WidgetName should ...', (tester) { ... })`

### Test Data

- Use factories for generating test data
- Keep test data consistent
- Clean up after tests
- Use fixtures for complex data structures

## 🐛 Troubleshooting

### Common Issues

1. **Tests are failing randomly**
   - Ensure tests are independent
   - Use `setUp` and `tearDown` properly
   - Avoid `setState` in tests

2. **Widget tests are timing out**
   - Use `pumpAndSettle` to wait for animations
   - Increase timeout if necessary
   - Avoid `sleep` in tests

3. **Mocks are not working**
   - Verify the mock is properly set up
   - Check that the method signatures match
   - Use `any` or `any(named: 'param')` for arguments

### Debugging Tests

```bash
# Run in debug mode
flutter test --start-paused

# Print debug output
flutter test -d <device_id> --start-paused -t my_test
```

## 📚 Additional Resources

- [Flutter Testing Documentation](https://flutter.dev/docs/testing)
- [Flutter Cookbook: Testing](https://flutter.dev/docs/cookbook/testing)
- [Mocktail Package](https://pub.dev/packages/mocktail)
- [Integration Testing](https://flutter.dev/docs/testing/integration-tests)
- [Golden Tests](https://pub.dev/packages/golden_toolkit)
