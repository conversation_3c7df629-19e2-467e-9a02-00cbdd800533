# VS Code Setup for GaadiSewa+ Development

This guide provides step-by-step instructions for setting up VS Code for optimal Flutter development on the GaadiSewa+ project.

## Table of Contents

- [Prerequisites](#-prerequisites)
- [Installation](#-installation)
- [Recommended Extensions](#-recommended-extensions)
- [Workspace Settings](#-workspace-settings)
- [Keybindings](#-keybindings)
- [Debugging](#-debugging)
- [Testing](#-testing)
- [Git Integration](#-git-integration)
- [Productivity Tips](#-productivity-tips)
- [Troubleshooting](#-troubleshooting)

## 📋 Prerequisites

- [VS Code](https://code.visualstudio.com/) (Latest stable version)
- [Flutter SDK](https://flutter.dev/docs/get-started/install) (3.19.0 or higher)
- [Dart SDK](https://dart.dev/get-dart) (3.3.0 or higher)
- [Git](https://git-scm.com/)
- [Flutter and Dart Extensions](#recommended-extensions)

## 💻 Installation

### 1. Install VS Code

Download and install VS Code from [code.visualstudio.com](https://code.visualstudio.com/).

### 2. Install Flutter and Dart Extensions

1. Open VS Code
2. Go to Extensions view (Ctrl+Shift+X or Cmd+Shift+X)
3. Search for and install the following extensions:
   - `Dart` (by Dart Code)
   - `Flutter` (by Dart Code)
   - `Pubspec Assist` (by Jeroen Meijer)
   - `Bloc` (by Felix Angelov)
   - `Riverpod Snippets` (by roipeker)
   - `Error Lens` (by Alexander)
   - `Dart Data Class Generator` (by Bendix Maier)

### 3. Configure Flutter SDK Path

1. Open Command Palette (Ctrl+Shift+P or Cmd+Shift+P)
2. Type "Flutter: Configure Flutter SDK"
3. Select your Flutter SDK installation directory

## 🧩 Recommended Extensions

### Core Extensions

| Extension | Purpose |
|-----------|---------|
| Dart | Dart language support |
| Flutter | Flutter framework support |
| Pubspec Assist | Manage dependencies easily |
| Error Lens | Show errors inline |
| Dart Data Class Generator | Generate data classes |

### Productivity

| Extension | Purpose |
|-----------|---------|
| GitLens | Enhanced Git capabilities |
| Todo Tree | Show TODO comments in explorer |
| Better Comments | Better comment highlighting |
| Path Intellisense | Autocomplete filenames |
| Auto Rename Tag | Auto-rename HTML/XML tags |

### Testing

| Extension | Purpose |
|-----------|---------|
| Flutter Coverage | Show test coverage |
| Test Explorer UI | Run and debug tests |
| Test Explorer | Alternative test explorer |

### UI/UX

| Extension | Purpose |
|-----------|---------|
| Material Icon Theme | Better file icons |
| Bracket Pair Colorizer 2 | Colorize matching brackets |
| Indent Rainbow | Colorize indentation |
| Prettier | Code formatter |

### Other Useful Extensions

| Extension | Purpose |
|-----------|---------|
| YAML | YAML language support |
| Markdown All in One | Markdown editing |
| REST Client | Test HTTP requests |
| Thunder Client | API testing |
| Live Share | Collaborative development |

## ⚙️ Workspace Settings

Create a `.vscode/settings.json` file in your project root with the following recommended settings:

```json
{
  "dart.debugExternalPackageLibraries": true,
  "dart.debugSdkLibraries": false,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "dart.showDartDeveloperLogs": true,
  "dart.warnWhenEditingFilesOutsideWorkspace": false,
  "dart.openDevTools": "flutter",
  "dart.flutterRunAdditionalArgs": ["--dart-define-from-file=.env"],
  "dart.flutterTestAdditionalArgs": ["--dart-define-from-file=.env.test"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "editor.defaultFormatter": "Dart-Code.dart-code",
  "editor.rulers": [80, 100],
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.associations": {
    "*.dart": "dart",
    "*.g.dart": "dart",
    "*.freezed.dart": "dart"
  },
  "search.exclude": {
    "**/.dart_tool": true,
    "**/.flutter-plugins": true,
    "**/.flutter-plugins-dependencies": true,
    "**/.pub-cache": true,
    "**/.pub": true,
    "**/build": true,
    "**/ios": true,
    "**/android": true,
    "**/web": true,
    "**/windows": true,
    "**/macos": true,
    "**/linux": true
  },
  "dart.analysisExcludedFolders": [
    "**/build/**",
    "**/.dart_tool/**",
    "**/packages/**"
  ],
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "workbench.iconTheme": "material-icon-theme",
  "workbench.colorTheme": "Default Dark+",
  "terminal.integrated.defaultProfile.windows": "Git Bash",
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.defaultProfile.linux": "bash",
  "emmet.includeLanguages": {
    "dart": "html",
    "html": "html"
  },
  "dart.lineLength": 80,
  "dart.analysisExcluded": [
    "**/*.freezed.dart",
    "**/*.g.dart"
  ],
  "dart.previewFlutterStructuredErrors": true,
  "dart.previewFlutterStructuredErrorsGroupBy": "project"
}
```

## ⌨️ Keybindings

### Essential Keybindings

| Command | Windows/Linux | macOS |
|---------|---------------|-------|
| Quick Open | Ctrl+P | Cmd+P |
| Command Palette | Ctrl+Shift+P | Cmd+Shift+P |
| Toggle Terminal | Ctrl+` | Cmd+` |
| Format Document | Shift+Alt+F | Shift+Option+F |
| Go to Definition | F12 | F12 |
| Find All References | Shift+F12 | Shift+F12 |
| Rename Symbol | F2 | F2 |
| Quick Fix | Ctrl+. | Cmd+. |
| Toggle Breakpoint | F9 | F9 |
| Start Debugging | F5 | F5 |
| Step Over | F10 | F10 |
| Step Into | F11 | F11 |
| Step Out | Shift+F11 | Shift+F11 |

### Flutter-Specific Keybindings

| Command | Windows/Linux | macOS |
|---------|---------------|-------|
| Flutter: Hot Reload | Ctrl+\ | Cmd+\ |
| Flutter: Hot Restart | Ctrl+Shift+\ | Cmd+Shift+\ |
| Flutter: Run | F5 | F5 |
| Flutter: Attach to Process | F5 | F5 |
| Flutter: Select Device | Ctrl+Shift+P > Flutter: Select Device | Cmd+Shift+P > Flutter: Select Device |
| Flutter: Emulators | Ctrl+Shift+P > Flutter: Launch Emulator | Cmd+Shift+P > Flutter: Launch Emulator |

### Custom Keybindings

Add these to `keybindings.json` (Open Command Palette > "Preferences: Open Keyboard Shortcuts (JSON)"):

```json
[
  {
    "key": "ctrl+alt+r",
    "command": "flutter.hotReload",
    "when": "dart-code:flutterProjectLoaded && !dart-code:debuggingFlutter"
  },
  {
    "key": "ctrl+alt+shift+r",
    "command": "flutter.hotRestart",
    "when": "dart-code:flutterProjectLoaded && !dart-code:debuggingFlutter"
  },
  {
    "key": "ctrl+alt+d",
    "command": "workbench.action.toggleDevTools",
    "when": "dart-code:devToolsAvailable"
  },
  {
    "key": "ctrl+alt+g",
    "command": "dart-import.preview",
    "when": "editorTextFocus && dartAnalyzerReady"
  }
]
```

## 🐞 Debugging

### Launch Configurations

Create a `.vscode/launch.json` file with these configurations:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "GaadiSewa+ (debug)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": ["--dart-define-from-file=.env"]
    },
    {
      "name": "GaadiSewa+ (profile)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "flutterMode": "profile",
      "args": ["--dart-define-from-file=.env"]
    },
    {
      "name": "GaadiSewa+ (release)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "flutterMode": "release",
      "args": ["--dart-define-from-file=.env"]
    },
    {
      "name": "GaadiSewa+ (web)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "deviceId": "chrome",
      "args": ["--web-renderer", "html", "--dart-define-from-file=.env"]
    }
  ]
}
```

### Debugging Tips

1. **Breakpoints**
   - Click in the gutter to set a breakpoint
   - Right-click for conditional breakpoints
   - Use `print()` or `debugPrint()` for quick debugging

2. **Debug Console**
   - Access the debug console with `Ctrl+Shift+Y` or `Cmd+Shift+Y`
   - Enter Dart expressions to evaluate

3. **Flutter Inspector**
   - Open with the "Flutter: Open DevTools" command
   - Inspect widget tree and performance

4. **Dart DevTools**
   - Launch with `F5` or the "Dart: Open DevTools" command
   - Access performance, memory, and network tools

## 🧪 Testing

### Running Tests

1. **Run All Tests**
   - Command Palette > "Dart: Run All Tests"
   - Or click the test icon in the sidebar

2. **Run a Single Test**
   - Click the "Run Test" button above a test
   - Or right-click and select "Run Test"

3. **Debug a Test**
   - Set breakpoints in your test file
   - Right-click and select "Debug Test"

### Test Explorer

1. Install the "Test Explorer UI" extension
2. Open the Test Explorer view
3. Run, debug, or watch tests directly from the explorer

## 🔄 Git Integration

### Built-in Git

- View changes in the Source Control view (Ctrl+Shift+G or Cmd+Shift+G)
- Stage changes with the + button
- Commit with a message and sync

### GitLens Features

- View git blame annotations
- See file history
- Compare changes between versions
- View commit details

### Recommended Git Settings

```json
{
  "git.autofetch": true,
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.enableCommitSigning": true,
  "gitlens.codeLens.enabled": true,
  "gitlens.hovers.enabled": true,
  "gitlens.historyExplorer.enabled": true
}
```

## 🚀 Productivity Tips

### Snippets

Create custom snippets in `.vscode/flutter.json`:

```json
{
  "Stateless Widget": {
    "prefix": "stateless",
    "body": [
      "import 'package:flutter/material.dart';",
      "",
      "class $1 extends StatelessWidget {",
      "  const $1({Key? key}) : super(key: key);",
      "",
      "  @override",
      "  Widget build(BuildContext context) {",
      "    return $0;",
      "  }",
      "}"
    ],
    "description": "Create a stateless widget"
  }
}
```

### Tasks

Create build tasks in `.vscode/tasks.json`:

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Flutter: Run",
      "type": "dart",
      "command": "flutter",
      "args": ["run", "--dart-define-from-file=.env"],
      "problemMatcher": []
    },
    {
      "label": "Flutter: Build APK",
      "type": "dart",
      "command": "flutter",
      "args": ["build", "apk", "--release", "--dart-define-from-file=.env"],
      "problemMatcher": []
    },
    {
      "label": "Flutter: Build Web",
      "type": "dart",
      "command": "flutter",
      "args": ["build", "web", "--release", "--web-renderer", "html", "--dart-define-from-file=.env"],
      "problemMatcher": []
    }
  ]
}
```

### Multi-root Workspace

For larger projects, use a multi-root workspace:

1. File > Add Folder to Workspace
2. Add all relevant directories
3. Save workspace as `gaadi-sewa.code-workspace`

## 🐛 Troubleshooting

### Common Issues

1. **Flutter/Dart Not Detected**
   - Ensure Flutter and Dart extensions are installed
   - Set the correct Flutter SDK path in settings
   - Reload VS Code

2. **Analysis Server Not Starting**
   - Run "Dart: Restart Analysis Server" from the command palette
   - Check the "Dart" output window for errors

3. **Hot Reload Not Working**
   - Ensure you're using a supported device/emulator
   - Try a full restart with `flutter clean`
   - Check for syntax errors that prevent compilation

4. **Performance Issues**
   - Exclude large directories in settings.json
   - Disable unnecessary extensions
   - Increase memory allocation if needed

### Getting Help

If you encounter issues:

1. Check the [Flutter documentation](https://flutter.dev/docs)
2. Search existing issues
3. Open a new issue with details:
   - VS Code version
   - Flutter/Dart versions
   - Steps to reproduce
   - Error logs from the "Dart" output channel

## 📚 Resources

- [VS Code Flutter Documentation](https://flutter.dev/docs/development/tools/vs-code)
- [Dart Code Extension](https://dartcode.org/)
- [Flutter DevTools](https://flutter.dev/docs/development/tools/devtools/overview)
- [VS Code Tips and Tricks](https://code.visualstudio.com/docs/getstarted/tips-and-tricks)
- [Productive Flutter Development in VS Code](https://medium.com/flutter/productive-flutter-development-in-vs-code-5fbd09f172b3)
