# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# Environment variables
.env
.env.*
!.env.example

# Android Studio/IntelliJ
.idea/
*.iml
*.ipr
*.iws
.idea_modules/

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Local History for Visual Studio Code
.history/

# Flutter/Dart/Pub
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Web related
lib/generated_plugin_registrant.dart

# Exceptions to above rules.
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

# Build outputs
build/

# iOS
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*.xcodeproj/xcuserdata
**/ios/**/*.xcworkspace/xcuserdata
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework/
**/ios/Flutter/Flutter.framework/
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Android
**/android/app/debug
**/android/app/profile
**/android/app/release
**/android/app/src/main/java/io/flutter/plugins/
**/android/gradle/wrapper/gradle-wrapper.properties
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/settings.gradle
**/android/.idea/
**/android/*.iml
**/android/*/
!**/android/app/
!**/android/app/src/main/res/
!**/android/app/src/main/AndroidManifest.xml
!**/android/app/build.gradle
!**/android/build.gradle
!**/android/settings.gradle
!**/android/keystore.properties

# macOS
**/macos/Flutter/Flutter-Output/
**/macos/Flutter/Flutter-Generated/
**/macos/Flutter/Flutter-Input/
**/macos/Flutter/Flutter-Intermediate/
**/macos/Flutter/Flutter-Output/
**/macos/Flutter/Flutter-Release/
**/macos/Flutter/Flutter-Profile/
**/macos/Flutter/Flutter-Debug/

# Windows
**/windows/flutter/generated_plugin_registrant.h
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugins.cmake
**/windows/flutter/generated_plugins.h
**/windows/flutter/generated_plugins.cc

# Linux
**/linux/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugins.cmake
**/linux/flutter/generated_plugins.h
**/linux/flutter/generated_plugins.cc

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
node_modules/
.packages

# Flutter/Dart
.flutter-plugins
.flutter-plugins-dependencies

# Web
web/
!.gitkeep

# IntelliJ
.idea/
*.iml
*.ipr
*.iws
.idea_modules/

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Local History for Visual Studio Code
.history/

# Flutter/Dart/Pub
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Web related
lib/generated_plugin_registrant.dart

# Exceptions to above rules.
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages
