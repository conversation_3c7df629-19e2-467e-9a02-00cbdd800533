import 'package:flutter_test/flutter_test.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';

void main() {
  group('VehicleModel Tests', () {
    late VehicleModel testVehicle;

    setUp(() {
      testVehicle = VehicleModel(
        id: 'test_id',
        ownerId: 'owner_123',
        type: VehicleType.car,
        make: 'Toyota',
        model: 'Corolla',
        year: 2020,
        licensePlate: 'BA 1 PA 1234',
        color: 'White',
        transmission: TransmissionType.manual,
        fuelType: FuelType.petrol,
        seatingCapacity: 5,
        dailyRate: 2000.0,
        location: const VehicleLocation(
          latitude: 27.7172,
          longitude: 85.3240,
          address: 'Kathmandu',
        ),
        description: 'Test vehicle description',
        features: const ['AC', 'GPS', 'Bluetooth'],
        images: const ['https://example.com/image1.jpg'],
        isAvailable: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    test('should create vehicle model with correct properties', () {
      // assert
      expect(testVehicle.id, 'test_id');
      expect(testVehicle.ownerId, 'owner_123');
      expect(testVehicle.type, VehicleType.car);
      expect(testVehicle.make, 'Toyota');
      expect(testVehicle.model, 'Corolla');
      expect(testVehicle.year, 2020);
      expect(testVehicle.licensePlate, 'BA 1 PA 1234');
      expect(testVehicle.color, 'White');
      expect(testVehicle.transmission, TransmissionType.manual);
      expect(testVehicle.fuelType, FuelType.petrol);
      expect(testVehicle.seatingCapacity, 5);
      expect(testVehicle.dailyRate, 2000.0);
      expect(testVehicle.location?.address, 'Kathmandu');
      expect(testVehicle.description, 'Test vehicle description');
      expect(testVehicle.features, ['AC', 'GPS', 'Bluetooth']);
      expect(testVehicle.images, ['https://example.com/image1.jpg']);
      expect(testVehicle.isAvailable, true);
    });

    test('should convert to JSON correctly', () {
      // act
      final json = testVehicle.toJson();

      // assert
      expect(json['id'], 'test_id');
      expect(json['ownerId'], 'owner_123');
      expect(json['type'], 'car');
      expect(json['make'], 'Toyota');
      expect(json['model'], 'Corolla');
      expect(json['year'], 2020);
      expect(json['licensePlate'], 'BA 1 PA 1234');
      expect(json['color'], 'White');
      expect(json['transmission'], 'manual');
      expect(json['fuelType'], 'petrol');
      expect(json['seatingCapacity'], 5);
      expect(json['dailyRate'], 2000.0);
      expect(json['description'], 'Test vehicle description');
      expect(json['features'], ['AC', 'GPS', 'Bluetooth']);
      expect(json['images'], ['https://example.com/image1.jpg']);
      expect(json['isAvailable'], true);
    });

    test('should create from JSON correctly', () {
      // arrange
      final json = {
        'id': 'test_id_2',
        'ownerId': 'owner_456',
        'type': 'scooter',
        'make': 'Honda',
        'model': 'CBR',
        'year': 2021,
        'licensePlate': 'BA 2 PA 5678',
        'color': 'Red',
        'transmission': 'manual',
        'fuelType': 'petrol',
        'seatingCapacity': 2,
        'dailyRate': 1500.0,
        'location': {
          'latitude': 27.7172,
          'longitude': 85.3240,
          'address': 'Pokhara',
        },
        'description': 'Test scooter',
        'features': ['ABS'],
        'images': ['test.jpg'],
        'isAvailable': false,
        'createdAt': '2023-01-01T00:00:00.000Z',
        'updatedAt': '2023-01-01T00:00:00.000Z',
      };

      // act
      final vehicle = VehicleModel.fromJson(json);

      // assert
      expect(vehicle.id, 'test_id_2');
      expect(vehicle.ownerId, 'owner_456');
      expect(vehicle.type, VehicleType.scooter);
      expect(vehicle.make, 'Honda');
      expect(vehicle.model, 'CBR');
      expect(vehicle.year, 2021);
      expect(vehicle.licensePlate, 'BA 2 PA 5678');
      expect(vehicle.color, 'Red');
      expect(vehicle.transmission, TransmissionType.manual);
      expect(vehicle.fuelType, FuelType.petrol);
      expect(vehicle.seatingCapacity, 2);
      expect(vehicle.dailyRate, 1500.0);
      expect(vehicle.location?.address, 'Pokhara');
      expect(vehicle.description, 'Test scooter');
      expect(vehicle.features, ['ABS']);
      expect(vehicle.images, ['test.jpg']);
      expect(vehicle.isAvailable, false);
    });

    test('should handle copyWith correctly', () {
      // act
      final updatedVehicle = testVehicle.copyWith(
        dailyRate: 2500.0,
        isAvailable: false,
        description: 'Updated description',
      );

      // assert
      expect(updatedVehicle.id, testVehicle.id); // unchanged
      expect(updatedVehicle.make, testVehicle.make); // unchanged
      expect(updatedVehicle.dailyRate, 2500.0); // changed
      expect(updatedVehicle.isAvailable, false); // changed
      expect(updatedVehicle.description, 'Updated description'); // changed
    });
  });
}
