import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/vehicles/data/repositories/vehicle_repository_impl.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Generate mocks
@GenerateMocks([
  NetworkInfo,
  SupabaseClient,
  SupabaseQueryBuilder,
  PostgrestFilterBuilder,
  PostgrestTransformBuilder,
])
import 'vehicle_repository_impl_test.mocks.dart';

void main() {
  // Simplified tests to avoid complex Supabase mocking issues
  late VehicleRepositoryImpl repository;
  late MockNetworkInfo mockNetworkInfo;
  late MockSupabaseClient mockSupabaseClient;

  setUp(() {
    mockNetworkInfo = MockNetworkInfo();
    mockSupabaseClient = MockSupabaseClient();

    repository = VehicleRepositoryImpl(
      networkInfo: mockNetworkInfo,
      supabaseClient: mockSupabaseClient,
    );
  });

  group('VehicleRepositoryImpl', () {
    group('Network connectivity', () {
      test('should throw NetworkException when network is not connected',
          () async {
        // Arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => repository.getVehicles(),
          throwsA(isA<NetworkException>()),
        );
      });
    });

    group('getAvailableVehicleFeatures', () {
      test('should return list of features', () async {
        // Act
        final result = await repository.getAvailableVehicleFeatures();

        // Assert
        expect(result, isA<List<String>>());
        expect(result, isNotEmpty);
        expect(result, contains('Air Conditioning'));
        expect(result, contains('Bluetooth'));
      });
    });
  });
}
