import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/messaging/data/repositories/messaging_repository_impl.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/conversation_model.dart';

import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Generate mocks
@GenerateMocks([
  NetworkInfo,
  SupabaseClient,
  SupabaseQueryBuilder,
  PostgrestFilterBuilder,
  PostgrestTransformBuilder,
])
import 'messaging_repository_impl_test.mocks.dart';

void main() {
  // Simplified tests to avoid complex Supabase mocking issues
  late MessagingRepositoryImpl repository;
  late MockNetworkInfo mockNetworkInfo;
  late MockSupabaseClient mockSupabaseClient;

  setUp(() {
    mockNetworkInfo = MockNetworkInfo();
    mockSupabaseClient = MockSupabaseClient();

    repository = MessagingRepositoryImpl(
      networkInfo: mockNetworkInfo,
      supabaseClient: mockSupabaseClient,
    );
  });

  group('MessagingRepositoryImpl', () {
    group('Network connectivity', () {
      test('should return NetworkFailure when network is not connected',
          () async {
        // Arrange
        const userId = 'user123';
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // Act
        final result = await repository.getUserConversations(userId);

        // Assert
        expect(result, isA<Left<Failure, List<ConversationModel>>>());
        result.fold(
          (failure) => expect(failure, isA<NetworkFailure>()),
          (conversations) => fail('Expected Left but got Right'),
        );
      });
    });
  });
}
