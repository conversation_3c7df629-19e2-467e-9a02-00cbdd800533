import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/bookings/data/repositories/booking_repository_impl.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockVehicleRepository extends Mock implements VehicleRepository {}

class MockSupabaseClient extends Mock implements SupabaseClient {}

void main() {
  late BookingRepositoryImpl repository;
  late MockNetworkInfo mockNetworkInfo;
  late MockVehicleRepository mockVehicleRepository;
  late MockSupabaseClient mockSupabaseClient;

  setUp(() {
    mockNetworkInfo = MockNetworkInfo();
    mockVehicleRepository = MockVehicleRepository();
    mockSupabaseClient = MockSupabaseClient();
    repository = BookingRepositoryImpl(
      networkInfo: mockNetworkInfo,
      vehicleRepository: mockVehicleRepository,
      supabaseClient: mockSupabaseClient,
    );
  });

  group('BookingRepository', () {
    const tVehicleId = 'vehicle_123';
    const tOwnerId = 'owner_123';

    final tVehicle = VehicleModel(
      id: tVehicleId,
      ownerId: tOwnerId,
      type: VehicleType.car,
      make: 'Toyota',
      model: 'Corolla',
      year: 2020,
      licensePlate: 'BA 1 PA 1234',
      color: 'White',
      transmission: TransmissionType.manual,
      fuelType: FuelType.petrol,
      seatingCapacity: 5,
      dailyRate: 2000.0,
      location: const VehicleLocation(
        latitude: 27.7172,
        longitude: 85.3240,
        address: 'Kathmandu',
      ),
      description: 'Test vehicle',
      features: const ['AC', 'GPS'],
      images: const ['image1.jpg'],
      isAvailable: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final tBooking = BookingModel(
      id: 'booking_123',
      userId: 'user_123',
      vehicleId: tVehicleId,
      vehicle: tVehicle,
      startDate: DateTime.now().add(const Duration(days: 1)),
      endDate: DateTime.now().add(const Duration(days: 3)),
      totalAmount: 6000.0, // 3 days * 2000 rate
      status: BookingStatus.pending,
      createdAt: DateTime.now(),
    );

    test('should check if device is online before creating booking', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

      // act
      final result = await repository.createBooking(tBooking);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(
          result,
          equals(
              const Left(NetworkFailure(message: 'No internet connection'))));
    });

    test('should calculate price correctly', () async {
      // arrange
      final startDate = DateTime.now().add(const Duration(days: 1));
      final endDate = DateTime.now().add(const Duration(days: 3));
      const expectedPrice =
          6000.0; // 3 days * 2000 rate (inclusive calculation)

      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => tVehicle);

      // act
      final result =
          await repository.calculatePrice(tVehicleId, startDate, endDate);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockVehicleRepository.getVehicleById(tVehicleId));
      expect(result, equals(const Right(expectedPrice)));
    });

    test('should return failure when vehicle not found for price calculation',
        () async {
      // arrange
      final startDate = DateTime.now().add(const Duration(days: 1));
      final endDate = DateTime.now().add(const Duration(days: 3));

      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => null);

      // act
      final result =
          await repository.calculatePrice(tVehicleId, startDate, endDate);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockVehicleRepository.getVehicleById(tVehicleId));
      expect(result, isA<Left>());
      expect((result as Left).value, isA<NotFoundFailure>());
    });
  });
}
