# Contributing to G<PERSON><PERSON>Sewa+ 🚀

Thank you for your interest in contributing to GaadiSewa+! We're excited to have you on board. This guide will help you get started with contributing to our project.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Code Style](#code-style)
- [Pull Request Process](#pull-request-process)
- [Reporting Issues](#reporting-issues)
- [Feature Requests](#feature-requests)
- [Testing](#testing)
- [Documentation](#documentation)

## 🤝 Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md). Please read it before contributing.

## 🚀 Getting Started

1. **Fork the repository**
   Click the "Fork" button in the top-right corner of the repository page.

2. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/gaadi-sewa.git
   cd gaadi-sewa
   ```

3. **Set up the development environment**
   ```bash
   # Install dependencies
   flutter pub get
   
   # Set up environment variables
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 🔄 Development Workflow

1. **Create a new branch**
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b fix/issue-number-description
   ```

2. **Make your changes**
   - Follow our code style guidelines
   - Write tests for new features
   - Update documentation as needed

3. **Run tests**
   ```bash
   flutter test
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```
   
   Use conventional commit messages:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation changes
   - `style:` for formatting changes
   - `refactor:` for code changes that neither fix bugs nor add features
   - `test:` for adding tests
   - `chore:` for maintenance tasks

5. **Push your changes**
   ```bash
   git push origin your-branch-name
   ```

6. **Create a Pull Request**
   - Go to the [Pull Requests](https://github.com/your-username/gaadi-sewa/pulls) page
   - Click "New Pull Request"
   - Select your branch
   - Fill in the PR template
   - Submit the PR

## 🎨 Code Style

- Follow the [Dart style guide](https://dart.dev/guides/language/effective-dart/style)
- Use `dart format` to format your code
- Keep lines under 80 characters
- Use meaningful variable and function names
- Add comments for complex logic
- Write tests for new features

## 🔍 Pull Request Process

1. Ensure any install or build dependencies are removed before the end of the layer when doing a build
2. Update the README.md with details of changes to the interface, this includes new environment variables, exposed ports, useful file locations and container parameters
3. Increase the version numbers in any examples files and the README.md to the new version that this Pull Request would represent. The versioning scheme we use is [SemVer](http://semver.org/)
4. Your PR will be reviewed by the maintainers. We may suggest changes or improvements

## 🐛 Reporting Issues

When creating an issue, please include:

1. A clear and descriptive title
2. Steps to reproduce the issue
3. Expected vs actual behavior
4. Screenshots or screen recordings if applicable
5. Device and OS version
6. Any relevant logs

## 💡 Feature Requests

We welcome feature requests! Please include:

1. A clear and descriptive title
2. A description of the problem you're trying to solve
3. Examples of how the feature would be used
4. Any alternative solutions you've considered

## 🧪 Testing

- Write unit tests for new features
- Test your changes on multiple devices/simulators
- Ensure all existing tests pass
- Test both success and error cases

## 📚 Documentation

- Update documentation when adding new features
- Add comments for complex logic
- Keep the README up to date
- Document any breaking changes

## 🙏 Thank You!

Your contributions help make GaadiSewa+ better for everyone. Thank you for taking the time to contribute!
