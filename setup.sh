#!/bin/bash

# GaadiSewa+ Development Environment Setup Script
# This script helps set up the development environment for GaadiSewa+

echo "🚀 Setting up GaadiSewa+ development environment..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    echo "   Visit: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# Check Flutter version
FLUTTER_VERSION=$(flutter --version | head -n 1 | awk '{print $2}')
echo "✅ Flutter $FLUTTER_VERSION is installed"

# Install dependencies
echo "📦 Installing dependencies..."
flutter pub get

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "🔧 Creating .env file..."
    cp .env.example .env
    echo "   Please update the .env file with your configuration"
else
    echo "✅ .env file already exists"
fi

# Check for required tools
echo "🔍 Checking for required tools..."

# Check for Android Studio
if [ -d "/Applications/Android Studio.app" ] || [ -d "$HOME/Android/Sdk" ]; then
    echo "✅ Android Studio is installed"
else
    echo "⚠️  Android Studio is not installed. Required for Android development."
    echo "   Visit: https://developer.android.com/studio"
fi

# Check for Xcode (macOS only)
if [[ "$OSTYPE" == "darwin"* ]]; then
    if xcode-select -p &> /dev/null; then
        echo "✅ Xcode is installed"
    else
        echo "⚠️  Xcode is not installed. Required for iOS development."
        echo "   Install from the App Store or run: xcode-select --install"
    fi
fi

# Check for VS Code extensions
echo "🔍 Checking for recommended VS Code extensions..."
if command -v code &> /dev/null; then
    EXTENSIONS=(
        "Dart-Code.dart-code"
        "Dart-Code.flutter"
        "nash.awesome-flutter-snippets"
        "aaron-bond.better-comments"
        "usernamehw.errorlens"
        "Nash.awesome-flutter-snippets"
        "mhutchie.git-graph"
        "eamodio.gitlens"
        "nhoizey.gremlins"
        "davidanson.vscode-markdownlint"
        "pflannery.vscode-versionlens"
        "redhat.vscode-yaml"
        "ms-vscode.vscode-typescript-next"
        "esbenp.prettier-vscode"
    )

    for extension in "${EXTENSIONS[@]}"; do
        if code --list-extensions | grep -q "$extension"; then
            echo "   ✅ $extension"
        else
            echo "   ⚠️  $extension (not installed)"
            read -p "      Install? [y/N] " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                code --install-extension "$extension"
            fi
        fi
    done
else
    echo "⚠️  VS Code is not installed or not in PATH"
fi

# Run Flutter doctor
echo "🩺 Running Flutter doctor..."
flutter doctor -v

# Generate localization files
echo "🌐 Generating localization files..."
flutter gen-l10n

# Setup Git hooks
echo "🔧 Setting up Git hooks..."
if [ -d ".git" ]; then
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOL'
#!/bin/sh

# Run formatter
flutter format .

# Run analyzer
flutter analyze

# Run tests
flutter test
EOL

    chmod +x .git/hooks/pre-commit
    echo "✅ Git pre-commit hook installed"
else
    echo "⚠️  Not a Git repository. Skipping Git hooks setup."
fi

# Install Flutter dependencies
echo "📦 Installing Flutter dependencies..."
flutter pub get

# Generate code for build_runner
echo "⚙️  Generating code..."
flutter pub run build_runner build --delete-conflicting-outputs

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update the .env file with your configuration"
echo "2. Run the app: flutter run -d chrome"
echo "3. Happy coding! 🚀"
echo ""

exit 0
